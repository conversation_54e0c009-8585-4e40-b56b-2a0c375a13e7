<?php

namespace RCF\Database;

use DateTimeInterface;
use <PERSON>F\Support\Formatting\DateNormalizer;

/**
 * Mock MongoDB Implementation
 * 
 * Provides a file-based implementation that mimics MongoDB operations
 * for development and testing when MongoDB is not available
 */
class MockMongoDB
{
    private string $dataDir;
    private array $collections = [];
    
    public function __construct(string $dataDir = 'storage/mock_mongodb')
    {
        $this->dataDir = $dataDir;
        $this->ensureDataDirectory();
        $this->loadCollections();
    }
    
    private function ensureDataDirectory(): void
    {
        if (!is_dir($this->dataDir)) {
            mkdir($this->dataDir, 0755, true);
        }
    }
    
    private function loadCollections(): void
    {
        $files = glob($this->dataDir . '/*.json');
        foreach ($files as $file) {
            $collectionName = basename($file, '.json');
            $data = json_decode(file_get_contents($file), true) ?: [];

            if (is_array($data)) {
                $fileTimestamp = filemtime($file) ?: time();
                $data = array_map(function ($document) use ($fileTimestamp) {
                    return is_array($document)
                        ? $this->repairLegacyTimestamps($document, $fileTimestamp)
                        : $document;
                }, $data);
            }

            $this->collections[$collectionName] = $data;
        }
    }
    
    private function saveCollection(string $name): void
    {
        $file = $this->dataDir . '/' . $name . '.json';
        file_put_contents($file, json_encode($this->collections[$name] ?? [], JSON_PRETTY_PRINT));
    }
    
    public function getCollection(string $name): MockCollection
    {
        if (!isset($this->collections[$name])) {
            $this->collections[$name] = [];
        }
        return new MockCollection($name, $this);
    }
    
    public function insertOne(string $collection, array $document): string
    {
        if (!isset($this->collections[$collection])) {
            $this->collections[$collection] = [];
        }
        
        // Generate ObjectId-like string
        $id = $this->generateObjectId();
        $document['_id'] = $id;
        $document['id'] = $id; // For compatibility
        $document = $this->normaliseDocument($document);

        $this->collections[$collection][] = $document;
        $this->saveCollection($collection);

        return $id;
    }
    
    public function findOne(string $collection, array $filter = []): ?array
    {
        $results = $this->find($collection, $filter, 1);
        return $results[0] ?? null;
    }
    
    public function find(string $collection, array $filter = [], int $limit = 0): array
    {
        if (!isset($this->collections[$collection])) {
            return [];
        }
        
        $results = $this->collections[$collection];
        
        // Apply filters
        if (!empty($filter)) {
            $results = array_filter($results, function($doc) use ($filter) {
                return $this->matchesFilter($doc, $filter);
            });
        }
        
        // Apply limit
        if ($limit > 0) {
            $results = array_slice($results, 0, $limit);
        }
        
        return array_values($results);
    }
    
    public function updateOne(string $collection, array $filter, array $update): bool
    {
        if (!isset($this->collections[$collection])) {
            return false;
        }
        
        foreach ($this->collections[$collection] as &$doc) {
            if ($this->matchesFilter($doc, $filter)) {
                if (isset($update['$set'])) {
                    $doc = array_merge($doc, $update['$set']);
                }
                if (isset($update['$inc'])) {
                    foreach ($update['$inc'] as $field => $value) {
                        $doc[$field] = ($doc[$field] ?? 0) + $value;
                    }
                }
                $doc['updated_at'] = date('Y-m-d H:i:s');
                $doc = $this->normaliseDocument($doc);
                $this->saveCollection($collection);
                return true;
            }
        }
        
        return false;
    }
    
    public function deleteOne(string $collection, array $filter): bool
    {
        if (!isset($this->collections[$collection])) {
            return false;
        }
        
        foreach ($this->collections[$collection] as $index => $doc) {
            if ($this->matchesFilter($doc, $filter)) {
                unset($this->collections[$collection][$index]);
                $this->collections[$collection] = array_values($this->collections[$collection]);
                $this->saveCollection($collection);
                return true;
            }
        }
        
        return false;
    }
    
    public function count(string $collection, array $filter = []): int
    {
        return count($this->find($collection, $filter));
    }
    
    public function aggregate(string $collection, array $pipeline): array
    {
        // Simple aggregation support
        $results = $this->collections[$collection] ?? [];
        
        foreach ($pipeline as $stage) {
            if (isset($stage['$match'])) {
                $results = array_filter($results, function($doc) use ($stage) {
                    return $this->matchesFilter($doc, $stage['$match']);
                });
            }
            
            if (isset($stage['$group'])) {
                $results = $this->groupDocuments($results, $stage['$group']);
            }
            
            if (isset($stage['$sort'])) {
                $results = $this->sortDocuments($results, $stage['$sort']);
            }
            
            if (isset($stage['$limit'])) {
                $results = array_slice($results, 0, $stage['$limit']);
            }
        }
        
        return array_values($results);
    }

    private function normaliseDocument(array $document): array
    {
        foreach ($document as $key => $value) {
            $document[$key] = $this->serialiseValue($value);
        }

        return $document;
    }

    private function repairLegacyTimestamps(array $document, int $fallbackTimestamp): array
    {
        foreach (['created_at', 'updated_at'] as $field) {
            if (!array_key_exists($field, $document)) {
                continue;
            }

            $value = $document[$field];
            if ($value instanceof DateTimeInterface || $this->isUtcDateTime($value)) {
                continue;
            }

            $isEmptyArray = is_array($value) && count($value) === 0;
            $isEmptyScalar = $value === null || $value === '';

            if ($isEmptyArray || $isEmptyScalar) {
                $document[$field] = date('Y-m-d H:i:s', $fallbackTimestamp);
            }
        }

        return $this->normaliseDocument($document);
    }

    private function serialiseValue($value)
    {
        if ($value instanceof DateTimeInterface || $this->isUtcDateTime($value)) {
            return DateNormalizer::normalise($value);
        }

        if (is_array($value)) {
            foreach ($value as $k => $v) {
                $value[$k] = $this->serialiseValue($v);
            }
        }

        return $value;
    }

    private function isUtcDateTime($value): bool
    {
        return class_exists('MongoDB\\BSON\\UTCDateTime') && $value instanceof \MongoDB\BSON\UTCDateTime;
    }

    private function matchesFilter(array $document, array $filter): bool
    {
        foreach ($filter as $field => $value) {
            if ($field === '_id' || $field === 'id') {
                if ($document['_id'] !== $value && $document['id'] !== $value) {
                    return false;
                }
            } elseif (is_array($value)) {
                // Handle operators like $in, $gte, etc.
                if (isset($value['$in'])) {
                    if (!in_array($document[$field] ?? null, $value['$in'])) {
                        return false;
                    }
                } elseif (isset($value['$gte'])) {
                    if (($document[$field] ?? 0) < $value['$gte']) {
                        return false;
                    }
                } elseif (isset($value['$lte'])) {
                    if (($document[$field] ?? 0) > $value['$lte']) {
                        return false;
                    }
                } elseif (isset($value['$regex'])) {
                    if (!preg_match('/' . $value['$regex'] . '/i', $document[$field] ?? '')) {
                        return false;
                    }
                }
            } else {
                if (($document[$field] ?? null) !== $value) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    private function groupDocuments(array $documents, array $groupSpec): array
    {
        $groups = [];
        
        foreach ($documents as $doc) {
            $groupKey = $this->evaluateGroupKey($doc, $groupSpec['_id']);
            
            if (!isset($groups[$groupKey])) {
                $groups[$groupKey] = ['_id' => $groupKey];
            }
            
            // Handle accumulators
            foreach ($groupSpec as $field => $accumulator) {
                if ($field === '_id') continue;
                
                if (isset($accumulator['$sum'])) {
                    $groups[$groupKey][$field] = ($groups[$groupKey][$field] ?? 0) + 
                        ($accumulator['$sum'] === 1 ? 1 : ($doc[$accumulator['$sum']] ?? 0));
                }
                
                if (isset($accumulator['$avg'])) {
                    // Simplified average calculation
                    $groups[$groupKey][$field . '_sum'] = ($groups[$groupKey][$field . '_sum'] ?? 0) + 
                        ($doc[$accumulator['$avg']] ?? 0);
                    $groups[$groupKey][$field . '_count'] = ($groups[$groupKey][$field . '_count'] ?? 0) + 1;
                    $groups[$groupKey][$field] = $groups[$groupKey][$field . '_sum'] / $groups[$groupKey][$field . '_count'];
                }
            }
        }
        
        return array_values($groups);
    }
    
    private function evaluateGroupKey(array $document, $groupId): string
    {
        if (is_string($groupId) && strpos($groupId, '$') === 0) {
            $field = substr($groupId, 1);
            return $document[$field] ?? 'null';
        }
        
        return (string)$groupId;
    }
    
    private function sortDocuments(array $documents, array $sortSpec): array
    {
        usort($documents, function($a, $b) use ($sortSpec) {
            foreach ($sortSpec as $field => $direction) {
                $aVal = $a[$field] ?? '';
                $bVal = $b[$field] ?? '';
                
                $cmp = $aVal <=> $bVal;
                if ($cmp !== 0) {
                    return $direction === -1 ? -$cmp : $cmp;
                }
            }
            return 0;
        });
        
        return $documents;
    }
    
    private function generateObjectId(): string
    {
        return bin2hex(random_bytes(12));
    }
    
    public function createIndex(string $collection, array $keys): void
    {
        // Mock index creation - just log it
        Logger::info("Mock index created", [
            'collection' => $collection,
            'keys' => $keys
        ]);
    }
}

class MockCollection
{
    private string $name;
    private MockMongoDB $db;
    
    public function __construct(string $name, MockMongoDB $db)
    {
        $this->name = $name;
        $this->db = $db;
    }
    
    public function insertOne(array $document): MockInsertResult
    {
        $id = $this->db->insertOne($this->name, $document);
        return new MockInsertResult($id);
    }
    
    public function findOne(array $filter = []): ?array
    {
        return $this->db->findOne($this->name, $filter);
    }
    
    public function find(array $filter = [], array $options = []): array
    {
        $limit = $options['limit'] ?? 0;
        return $this->db->find($this->name, $filter, $limit);
    }
    
    public function updateOne(array $filter, array $update): MockUpdateResult
    {
        $modified = $this->db->updateOne($this->name, $filter, $update);
        return new MockUpdateResult($modified);
    }
    
    public function deleteOne(array $filter): MockDeleteResult
    {
        $deleted = $this->db->deleteOne($this->name, $filter);
        return new MockDeleteResult($deleted);
    }
    
    public function countDocuments(array $filter = []): int
    {
        return $this->db->count($this->name, $filter);
    }
    
    public function aggregate(array $pipeline): array
    {
        return $this->db->aggregate($this->name, $pipeline);
    }
    
    public function createIndex(array $keys, array $options = []): void
    {
        $this->db->createIndex($this->name, $keys);
    }
}

class MockInsertResult
{
    private string $insertedId;
    
    public function __construct(string $insertedId)
    {
        $this->insertedId = $insertedId;
    }
    
    public function getInsertedId(): string
    {
        return $this->insertedId;
    }
}

class MockUpdateResult
{
    private bool $modified;
    
    public function __construct(bool $modified)
    {
        $this->modified = $modified;
    }
    
    public function getModifiedCount(): int
    {
        return $this->modified ? 1 : 0;
    }
}

class MockDeleteResult
{
    private bool $deleted;
    
    public function __construct(bool $deleted)
    {
        $this->deleted = $deleted;
    }
    
    public function getDeletedCount(): int
    {
        return $this->deleted ? 1 : 0;
    }
}

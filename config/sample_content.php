<?php

return [
    'categories' => [
        [
            'id' => 'sample-category-1',
            'name' => 'Organic Vegetable',
            'slug' => 'organic-vegetable',
            'description' => 'Fresh and organic vegetables sourced from local farms.',
            'status' => 'active',
            'image_path' => 'assets/images/category/01.png',
        ],
        [
            'id' => 'sample-category-2',
            'name' => 'Fresh Fruits',
            'slug' => 'fresh-fruits',
            'description' => 'Seasonal fruits delivered daily.',
            'status' => 'active',
            'image_path' => 'assets/images/category/02.png',
        ],
        [
            'id' => 'sample-category-3',
            'name' => 'Dairy Items',
            'slug' => 'dairy-items',
            'description' => 'Milk, cheese, and more dairy essentials.',
            'status' => 'active',
            'image_path' => 'assets/images/category/03.png',
        ],
        [
            'id' => 'sample-category-4',
            'name' => 'Dry Fruits',
            'slug' => 'dry-fruits',
            'description' => 'Healthy and delicious dry fruits.',
            'status' => 'active',
            'image_path' => 'assets/images/category/04.png',
        ],
        [
            'id' => 'sample-category-5',
            'name' => 'Bakery',
            'slug' => 'bakery',
            'description' => 'Breads, pastries, and baked goods.',
            'status' => 'active',
            'image_path' => 'assets/images/category/05.png',
        ],
        [
            'id' => 'sample-category-6',
            'name' => 'Beverages',
            'slug' => 'beverages',
            'description' => 'Juices, sodas, and more beverages.',
            'status' => 'active',
            'image_path' => 'assets/images/category/06.png',
        ],
        [
            'id' => 'sample-category-7',
            'name' => 'Snacks',
            'slug' => 'snacks',
            'description' => 'Crunchy and savory snacks for every taste.',
            'status' => 'active',
            'image_path' => 'assets/images/category/07.png',
        ],
        [
            'id' => 'sample-category-8',
            'name' => 'Meat & Seafood',
            'slug' => 'meat-seafood',
            'description' => 'High-quality meats and seafood.',
            'status' => 'active',
            'image_path' => 'assets/images/category/08.png',
        ],
        [
            'id' => 'sample-category-9',
            'name' => 'Organic Essentials',
            'slug' => 'organic-essentials',
            'description' => 'Pantry staples made from organic ingredients.',
            'status' => 'active',
            'image_path' => 'assets/images/category/09.png',
        ],
        [
            'id' => 'sample-category-10',
            'name' => 'Groceries',
            'slug' => 'groceries',
            'description' => 'Daily grocery essentials for your household.',
            'status' => 'active',
            'image_path' => 'assets/images/category/10.png',
        ],
    ],
    'products' => [
        [
            'id' => 'sample-product-cerelac',
            'name' => 'Nestle Cerelac Mixed Fruits & Wheat with Milk',
            'slug' => 'nestle-cerelac-mixed-fruits',
            'description' => 'Nutritious baby food with mixed fruits and wheat.',
            'price' => 36.00,
            'sale_price' => 30.00,
            'stock_quantity' => 120,
            'status' => 'active',
            'featured' => true,
            'category_id' => 'sample-category-1',
            'category_slug' => 'organic-vegetable',
            'image_path' => 'assets/images/grocery/01.jpg',
            'unit_label' => '500g Pack',
            'sales_count' => 180,
            'tags' => ['baby food', 'nutrition'],
        ],
        [
            'id' => 'sample-product-cheese',
            'name' => 'Peysan Full Fat Fresh Cottage Cheese',
            'slug' => 'peysan-cottage-cheese',
            'description' => 'Fresh and creamy cottage cheese perfect for any meal.',
            'price' => 25.00,
            'sale_price' => null,
            'stock_quantity' => 58,
            'status' => 'active',
            'featured' => true,
            'category_id' => 'sample-category-3',
            'category_slug' => 'dairy-items',
            'image_path' => 'assets/images/grocery/02.jpg',
            'unit_label' => '350g Pack',
            'sales_count' => 96,
            'tags' => ['dairy', 'cheese'],
        ],
        [
            'id' => 'sample-product-granola',
            'name' => 'Organic Honey Toasted Granola',
            'slug' => 'organic-honey-granola',
            'description' => 'Crunchy granola sweetened with organic honey.',
            'price' => 12.50,
            'sale_price' => 10.50,
            'stock_quantity' => 200,
            'status' => 'active',
            'featured' => false,
            'category_id' => 'sample-category-9',
            'category_slug' => 'organic-essentials',
            'image_path' => 'assets/images/grocery/03.jpg',
            'unit_label' => '400g Pack',
            'sales_count' => 240,
            'tags' => ['breakfast', 'organic'],
        ],
        [
            'id' => 'sample-product-marshmallow',
            'name' => 'Tokyo Style Marshmallow',
            'slug' => 'tokyo-style-marshmallow',
            'description' => 'Soft and sweet marshmallows inspired by Tokyo treats.',
            'price' => 15.00,
            'sale_price' => 12.00,
            'stock_quantity' => 320,
            'status' => 'active',
            'featured' => true,
            'category_id' => 'sample-category-7',
            'category_slug' => 'snacks',
            'image_path' => 'assets/images/grocery/05.jpg',
            'unit_label' => 'Pack of 12',
            'sales_count' => 310,
            'tags' => ['sweets', 'snacks'],
        ],
        [
            'id' => 'sample-product-juice',
            'name' => 'Alpro Organic Flavored Fresh Juice',
            'slug' => 'alpro-organic-juice',
            'description' => 'Refreshing juice made from organic ingredients.',
            'price' => 8.99,
            'sale_price' => 7.49,
            'stock_quantity' => 150,
            'status' => 'active',
            'featured' => false,
            'category_id' => 'sample-category-6',
            'category_slug' => 'beverages',
            'image_path' => 'assets/images/grocery/06.jpg',
            'unit_label' => '1L Bottle',
            'sales_count' => 205,
            'tags' => ['beverage', 'organic'],
        ],
        [
            'id' => 'sample-product-nuts',
            'name' => 'Crunchy Nuts Mix',
            'slug' => 'crunchy-nuts-mix',
            'description' => 'A savory mix of roasted nuts with sea salt.',
            'price' => 18.75,
            'sale_price' => null,
            'stock_quantity' => 90,
            'status' => 'active',
            'featured' => false,
            'category_id' => 'sample-category-7',
            'category_slug' => 'snacks',
            'image_path' => 'assets/images/grocery/07.jpg',
            'unit_label' => '450g Jar',
            'sales_count' => 132,
            'tags' => ['snacks', 'nuts'],
        ],
        [
            'id' => 'sample-product-bread',
            'name' => 'Artisanal Sourdough Bread',
            'slug' => 'artisanal-sourdough-bread',
            'description' => 'Hand-crafted sourdough loaf baked fresh daily.',
            'price' => 6.40,
            'sale_price' => null,
            'stock_quantity' => 45,
            'status' => 'active',
            'featured' => false,
            'category_id' => 'sample-category-5',
            'category_slug' => 'bakery',
            'image_path' => 'assets/images/grocery/08.jpg',
            'unit_label' => '700g Loaf',
            'sales_count' => 88,
            'tags' => ['bread', 'bakery'],
        ],
        [
            'id' => 'sample-product-salmon',
            'name' => 'Norwegian Smoked Salmon',
            'slug' => 'norwegian-smoked-salmon',
            'description' => 'Premium smoked salmon with rich flavor.',
            'price' => 22.90,
            'sale_price' => 19.90,
            'stock_quantity' => 65,
            'status' => 'active',
            'featured' => true,
            'category_id' => 'sample-category-8',
            'category_slug' => 'meat-seafood',
            'image_path' => 'assets/images/grocery/09.jpg',
            'unit_label' => '300g Pack',
            'sales_count' => 150,
            'tags' => ['seafood', 'premium'],
        ],
    ],
];

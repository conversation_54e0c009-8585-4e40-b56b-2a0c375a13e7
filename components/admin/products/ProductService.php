<?php

require_once __DIR__ . '/../../shared/FileUploadService.php';

use RCF\Support\Formatting\DateNormalizer;

class ProductService
{
    private string $uploadDirectory;
    private string $uploadRelativePath;
    private string $projectRoot;
    private FileUploadService $fileUploadService;

    /**
     * @var array<int, array{disk: string, absolute: string, public: ?string}>
     */
    private array $uploadLocations;

    public function __construct(private \RCF\Repositories\ProductRepository $repository, ?string $uploadDirectory = null)
    {
        $this->projectRoot = dirname(__DIR__, 3);
        $this->uploadRelativePath = 'assets/uploads/products';
        $this->uploadDirectory = $uploadDirectory ?? $this->projectRoot . '/' . $this->uploadRelativePath;
        $this->uploadLocations = $this->initialiseUploadLocations($uploadDirectory);
        $this->fileUploadService = new FileUploadService($this->uploadLocations, [
            'max_naming_attempts' => 15,
            'allowed_extensions' => ['jpg', 'jpeg', 'png', 'gif', 'webp'],
            'max_file_size' => 5 * 1024 * 1024, // 5MB for products
        ]);
    }

    public function getStatusOptions(): array
    {
        return [
            'active' => 'Active',
            'inactive' => 'Inactive',
            'draft' => 'Draft',
            'out_of_stock' => 'Out of stock',
        ];
    }

    public function getProduct(string $id): ?array
    {
        $product = $this->repository->findById($id);
        return $product !== null ? $this->normaliseProduct($product) : null;
    }

    /**
     * @return array{data: array<int, array<string, mixed>>, total: int, total_pages: int, current_page: int}
     */
    public function paginateProducts(int $page, int $perPage, ?string $search = null, ?string $status = null, ?string $categoryId = null): array
    {
        $page = max(1, $page);
        $perPage = max(1, $perPage);

        $filter = [];

        if ($status !== null && $status !== '') {
            $filter['status'] = $status;
        }

        if ($categoryId !== null && $categoryId !== '') {
            $filter['category_id'] = $categoryId;
        }

        if ($search !== null && $search !== '') {
            $filter['$or'] = [
                ['name' => ['$regex' => $search, '$options' => 'i']],
                ['description' => ['$regex' => $search, '$options' => 'i']],
                ['sku' => ['$regex' => $search, '$options' => 'i']],
            ];
        }

        $result = $this->repository->paginate($filter, $page, $perPage, ['created_at' => -1]);

        $total = (int) ($result['pagination']['total'] ?? 0);
        $totalPages = (int) ($result['pagination']['total_pages'] ?? 1);
        $totalPages = max(1, $totalPages);
        $currentPage = min(max(1, $page), $totalPages);

        $items = array_map(fn (array $product) => $this->normaliseProduct($product), $result['items']);

        return [
            'data' => $items,
            'total' => $total,
            'total_pages' => $totalPages,
            'current_page' => $currentPage,
        ];
    }

    /**
     * @return array{success: bool, errors: array<string, string>, message: string|null}
     */
    public function createProduct(array $input, ?array $imageUpload = null): array
    {
        $validation = ProductValidator::validate($input, $imageUpload);

        if (!empty($validation['errors'])) {
            return [
                'success' => false,
                'errors' => $validation['errors'],
                'message' => 'Please correct the highlighted errors and try again.',
            ];
        }

        $data = $validation['valid'];
        $imageFile = $data['image_upload'] ?? null;
        unset($data['image_upload']);
        
        // Generate unique slug
        $data['slug'] = $this->generateUniqueSlug($data['name']);

        // Handle image upload
        if ($imageFile !== null) {
            $storageResult = $this->storeProductImage($imageFile);
            if (!$storageResult['success']) {
                return [
                    'success' => false,
                    'errors' => ['image' => $storageResult['error']],
                    'message' => 'Please correct the highlighted errors and try again.',
                ];
            }
            $data['image_path'] = $storageResult['path'];
            $data['image_filename'] = $storageResult['filename'];
        }

        try {
            $productId = $this->repository->createProduct($data);
            return [
                'success' => true,
                'errors' => [],
                'message' => 'Product created successfully.',
                'product_id' => $productId,
            ];
        } catch (\Throwable $exception) {
            // Clean up uploaded image if product creation fails
            if (!empty($data['image_filename'])) {
                $this->deleteProductImage($data['image_path']);
            }
            
            return [
                'success' => false,
                'errors' => ['general' => 'Failed to save product. Please try again.'],
                'message' => 'We couldn\'t save the product right now.',
            ];
        }
    }

    /**
     * @return array{success: bool, errors: array<string, string>, message: string|null}
     */
    public function updateProduct(string $id, array $input, ?array $imageUpload = null): array
    {
        $existing = $this->repository->findById($id);
        if ($existing === null) {
            return [
                'success' => false,
                'errors' => ['general' => 'Product not found.'],
                'message' => 'The product you are trying to update no longer exists.',
            ];
        }

        $validation = ProductValidator::validate($input, $imageUpload, true);
        if (!empty($validation['errors'])) {
            return [
                'success' => false,
                'errors' => $validation['errors'],
                'message' => 'Please correct the highlighted errors and try again.',
            ];
        }

        $data = $validation['valid'];
        $imageFile = $data['image_upload'] ?? null;
        unset($data['image_upload']);
        
        // Generate unique slug (excluding current product)
        $data['slug'] = $this->generateUniqueSlug($data['name'], $existing['id']);

        // Handle image upload
        if ($imageFile !== null) {
            $storageResult = $this->storeProductImage($imageFile);
            if (!$storageResult['success']) {
                return [
                    'success' => false,
                    'errors' => ['image' => $storageResult['error']],
                    'message' => 'Please correct the highlighted errors and try again.',
                ];
            }

            // Delete old image
            if (!empty($existing['image_path'])) {
                $this->deleteProductImage($existing['image_path']);
            }

            $data['image_path'] = $storageResult['path'];
            $data['image_filename'] = $storageResult['filename'];
        } else {
            // Keep existing image
            $data['image_path'] = $existing['image_path'] ?? null;
            $data['image_filename'] = $existing['image_filename'] ?? null;
        }

        try {
            $this->repository->updateById($id, $data);
            return [
                'success' => true,
                'errors' => [],
                'message' => 'Product updated successfully.',
            ];
        } catch (\Throwable $exception) {
            return [
                'success' => false,
                'errors' => ['general' => 'Failed to update product. Please try again.'],
                'message' => 'We couldn\'t update the product right now.',
            ];
        }
    }

    /**
     * Delete a product and its associated image
     */
    public function deleteProduct(string $id): array
    {
        $existing = $this->repository->findById($id);
        if ($existing === null) {
            return [
                'success' => false,
                'errors' => ['general' => 'Product not found.'],
                'message' => 'The product you are trying to delete no longer exists.',
            ];
        }

        try {
            // Delete the product from database
            $deleted = $this->repository->deleteById($id);
            
            if ($deleted) {
                // Delete associated image
                if (!empty($existing['image_path'])) {
                    $this->deleteProductImage($existing['image_path']);
                }
                
                return [
                    'success' => true,
                    'errors' => [],
                    'message' => 'Product deleted successfully.',
                ];
            } else {
                return [
                    'success' => false,
                    'errors' => ['general' => 'Failed to delete product.'],
                    'message' => 'We couldn\'t delete the product right now.',
                ];
            }
        } catch (\Throwable $exception) {
            return [
                'success' => false,
                'errors' => ['general' => 'Failed to delete product. Please try again.'],
                'message' => 'We couldn\'t delete the product right now.',
            ];
        }
    }

    /**
     * Get all products with pagination
     */
    public function listProducts(int $page = 1, int $limit = 20): array
    {
        $pagination = $this->paginateProducts($page, $limit);
        return $pagination['data'];
    }

    /**
     * Search products
     */
    public function searchProducts(string $searchTerm, array $filters = [], int $limit = 20): array
    {
        return $this->repository->searchProducts($searchTerm, $filters, $limit);
    }

    /**
     * Store product image using enhanced file upload service
     * 
     * @param array{tmp_name:string, name?:string, extension:string} $imageFile
     * @return array{success: bool, path?: string, filename?: string, error?: string}
     */
    private function storeProductImage(array $imageFile): array
    {
        // Use hybrid naming strategy for products (timestamp + random)
        $result = $this->fileUploadService->uploadFile(
            $imageFile,
            FileUploadService::STRATEGY_HYBRID,
            'product-image'
        );

        if (!$result['success']) {
            return [
                'success' => false,
                'error' => $result['error'] ?? 'Unable to store product image.',
            ];
        }

        return [
            'success' => true,
            'path' => $result['path'],
            'filename' => $result['filename'],
        ];
    }

    /**
     * Delete product image from all storage locations
     */
    private function deleteProductImage(string $imagePath): void
    {
        // Extract filename from path
        $filename = basename($imagePath);
        
        foreach ($this->uploadLocations as $location) {
            $filePath = rtrim($location['absolute'], "/\\") . '/' . $filename;
            if (is_file($filePath)) {
                @unlink($filePath);
            }
        }
    }

    /**
     * Generate unique slug for product
     */
    private function generateUniqueSlug(string $name, ?string $excludeId = null): string
    {
        $baseSlug = $this->fileUploadService->slugifyFilename($name);
        if ($baseSlug === '') {
            $baseSlug = 'product';
        }

        $slug = $baseSlug;
        $counter = 1;

        while ($this->slugExists($slug, $excludeId)) {
            $slug = $baseSlug . '-' . $counter;
            $counter++;
        }

        return $slug;
    }

    /**
     * Check if slug exists (excluding specific product)
     */
    private function slugExists(string $slug, ?string $excludeId = null): bool
    {
        $existing = $this->repository->findBySlug($slug);
        
        if ($existing === null) {
            return false;
        }

        // If excluding a specific ID, check if it's the same product
        if ($excludeId !== null && isset($existing['id'])) {
            return (string) $existing['id'] !== $excludeId;
        }

        return true;
    }

    /**
     * Initialize upload locations with fallback options
     * 
     * @return array<int, array{disk: string, absolute: string, public: ?string}>
     */
    private function initialiseUploadLocations(?string $customDirectory): array
    {
        $locations = [];

        if ($customDirectory !== null) {
            $absolute = $this->resolvePathRelativeToRoot($customDirectory);
            $locations[] = [
                'disk' => 'custom',
                'absolute' => $absolute,
                'public' => $this->derivePublicPath($absolute),
            ];
            return $locations;
        }

        // Primary location: assets/uploads/products
        $locations[] = [
            'disk' => 'assets',
            'absolute' => $this->projectRoot . '/' . $this->uploadRelativePath,
            'public' => $this->uploadRelativePath,
        ];

        // Fallback location: storage/uploads/products
        $locations[] = [
            'disk' => 'storage',
            'absolute' => $this->projectRoot . '/storage/uploads/products',
            'public' => 'storage/uploads/products',
        ];

        // Emergency fallback: temp directory
        $locations[] = [
            'disk' => 'temp',
            'absolute' => rtrim(sys_get_temp_dir(), DIRECTORY_SEPARATOR) . '/rcf-product-images',
            'public' => null, // Will use serve-product-image.php
        ];

        return $locations;
    }

    /**
     * Resolve path relative to project root
     */
    private function resolvePathRelativeToRoot(string $path): string
    {
        if (str_starts_with($path, '/')) {
            return $path;
        }
        return $this->projectRoot . '/' . ltrim($path, '/');
    }

    /**
     * Derive public path from absolute path
     */
    private function derivePublicPath(string $absolutePath): ?string
    {
        $relativePath = str_replace($this->projectRoot . '/', '', $absolutePath);

        // Only return public path if it's within the web-accessible directory
        if (!str_starts_with($relativePath, '../') && !str_starts_with($absolutePath, '/tmp')) {
            return $relativePath;
        }

        return null;
    }

    private function normaliseProduct(array $product): array
    {
        $product['status'] = $product['status'] ?? 'active';
        $product['image_path'] = $product['image_path'] ?? null;
        $product['image_filename'] = $product['image_filename'] ?? null;
        $product['stock_quantity'] = isset($product['stock_quantity']) ? (int) $product['stock_quantity'] : 0;
        $product['price'] = isset($product['price']) ? (float) $product['price'] : 0.0;
        $product['sale_price'] = isset($product['sale_price']) && $product['sale_price'] !== null ? (float) $product['sale_price'] : null;
        $tags = [];
        if (isset($product['tags'])) {
            $rawTags = $product['tags'];
            if ($rawTags instanceof \Traversable) {
                $rawTags = iterator_to_array($rawTags, false);
            }

            if (is_array($rawTags)) {
                $tags = array_values(array_filter(array_map('trim', $rawTags), static fn ($value) => $value !== ''));
            } elseif (is_string($rawTags)) {
                $pieces = array_map('trim', explode(',', $rawTags));
                $tags = array_values(array_filter($pieces, static fn ($value) => $value !== ''));
            }
        }
        $product['tags'] = $tags;
        $product['featured'] = !empty($product['featured']);
        $product['weight'] = isset($product['weight']) && $product['weight'] !== null ? (float) $product['weight'] : null;
        foreach (['length', 'width', 'height'] as $dimension) {
            if (isset($product[$dimension]) && $product[$dimension] !== null && $product[$dimension] !== '') {
                $product[$dimension] = (float) $product[$dimension];
            } else {
                $product[$dimension] = null;
            }
        }

        if (isset($product['category_id']) && $product['category_id'] instanceof \MongoDB\BSON\ObjectId) {
            $product['category_id'] = (string) $product['category_id'];
        }

        foreach (['created_at', 'updated_at'] as $timestampField) {
            if (array_key_exists($timestampField, $product)) {
                $formatted = DateNormalizer::normalise($product[$timestampField]);
                if ($formatted !== null) {
                    $product[$timestampField] = $formatted;
                }
            }
        }

        return $product;
    }
}

<?php

if (!function_exists('site_config')) {
    function site_config(?string $path = null, $default = null)
    {
        static $config;
        if ($config === null) {
            $config = require __DIR__ . '/../../../config/site.php';
        }

        if ($path === null) {
            return $config;
        }

        $segments = explode('.', $path);
        $value = $config;

        foreach ($segments as $segment) {
            if (is_array($value) && array_key_exists($segment, $value)) {
                $value = $value[$segment];
            } else {
                return $default;
            }
        }

        return $value;
    }
}

if (!function_exists('array_access')) {
    function array_access(array $array, string $key, $default = null)
    {
        return $array[$key] ?? $default;
    }
}

if (!function_exists('html_attr')) {
    function html_attr(string $name, ?string $value): string
    {
        if ($value === null || $value === '') {
            return '';
        }

        return sprintf(' %s="%s"', $name, htmlspecialchars($value, ENT_QUOTES, 'UTF-8'));
    }
}

if (!function_exists('html_classes')) {
    function html_classes(array $classes): string
    {
        $filtered = array_filter(array_map('trim', $classes));
        if (empty($filtered)) {
            return '';
        }

        return htmlspecialchars(implode(' ', $filtered), ENT_QUOTES, 'UTF-8');
    }
}

if (!function_exists('render_partial')) {
    function render_partial(string $path, array $variables = []): void
    {
        extract($variables, EXTR_OVERWRITE);
        include $path;
    }
}

if (!function_exists('render_nav_links')) {
    /**
     * Renders a simple unordered list of navigation links.
     */
    function render_nav_links(array $links, array $options = []): void
    {
        $linkClass = $options['link_class'] ?? '';
        foreach ($links as $link) {
            $href = $link['href'] ?? '#';
            $label = $link['label'] ?? '';
            $class = trim($linkClass . ' ' . ($link['class'] ?? ''));
            echo '<li><a href="' . htmlspecialchars($href, ENT_QUOTES, 'UTF-8') . '"' . ($class ? html_attr('class', $class) : '') . '>' . htmlspecialchars($label, ENT_QUOTES, 'UTF-8') . '</a></li>';
        }
    }
}

if (!function_exists('render_category_menu')) {
    /**
     * Renders the multi-level category menu structure.
     */
    function render_category_menu(array $categories, array $options = []): void
    {
        $itemLinkClass = $options['item_link_class'] ?? 'menu-item';
        $submenuClass = $options['submenu_class'] ?? 'submenu';
        $submenuLinkClass = $options['submenu_link_class'] ?? 'mobile-menu-link';
        $showIcons = $options['show_icons'] ?? true;

        foreach ($categories as $category) {
            $label = $category['label'] ?? '';
            $href = $category['href'] ?? '#';
            $icon = $category['icon'] ?? null;
            $submenu = $category['submenu'] ?? [];
            $hasSubmenu = !empty($submenu);

            echo '<li>';
            echo '<a href="' . htmlspecialchars($href, ENT_QUOTES, 'UTF-8') . '" class="' . htmlspecialchars($itemLinkClass, ENT_QUOTES, 'UTF-8') . '">';
            if ($showIcons && $icon) {
                echo '<img src="' . htmlspecialchars($icon, ENT_QUOTES, 'UTF-8') . '" alt="icons">';
            }
            echo '<span>' . htmlspecialchars($label, ENT_QUOTES, 'UTF-8') . '</span>';
            if ($hasSubmenu) {
                echo '<i class="fa-regular fa-plus"></i>';
            }
            echo '</a>';

            if ($hasSubmenu) {
                echo '<ul class="' . htmlspecialchars($submenuClass, ENT_QUOTES, 'UTF-8') . '">';
                foreach ($submenu as $submenuLabel) {
                    if (is_array($submenuLabel)) {
                        // support associative submenu definitions
                        $submenuHref = $submenuLabel['href'] ?? '#';
                        $submenuText = $submenuLabel['label'] ?? '';
                    } else {
                        $submenuHref = '#';
                        $submenuText = $submenuLabel;
                    }
                    echo '<li><a class="' . htmlspecialchars($submenuLinkClass, ENT_QUOTES, 'UTF-8') . '" href="' . htmlspecialchars($submenuHref, ENT_QUOTES, 'UTF-8') . '">' . htmlspecialchars($submenuText, ENT_QUOTES, 'UTF-8') . '</a></li>';
                }
                echo '</ul>';
            }

            echo '</li>';
        }
    }
}

if (!function_exists('resolve_category_image_url')) {
    function resolve_category_image_url(?string $path): ?string
    {
        if ($path === null) {
            return null;
        }

        $trimmed = trim($path);
        if ($trimmed === '') {
            return null;
        }

        if (preg_match('/^https?:\/\//i', $trimmed) === 1) {
            return $trimmed;
        }

        $parsed = parse_url($trimmed);
        if ($parsed !== false && isset($parsed['path']) && str_ends_with($parsed['path'], 'serve-category-image.php')) {
            return $trimmed;
        }

        $projectRoot = dirname(__DIR__, 3);
        $relative = ltrim($trimmed, '/');
        $absolute = $projectRoot . '/' . $relative;

        if (is_file($absolute)) {
            return '/' . ltrim($trimmed, '/');
        }

        $filename = basename($relative);
        if (
            $filename !== ''
            && preg_match('/^[a-z0-9]+(?:-[a-z0-9]+)*-[0-9]{8}-[0-9]{6}(?:-[0-9]+)?\.(jpg|jpeg|png|webp)$/i', $filename) === 1
        ) {
            $fallbacks = [
                'assets' => $projectRoot . '/assets/uploads/categories/' . $filename,
                'storage' => $projectRoot . '/storage/uploads/categories/' . $filename,
                'temp' => rtrim(sys_get_temp_dir(), DIRECTORY_SEPARATOR) . '/rcf-category-images/' . $filename,
            ];

            foreach ($fallbacks as $disk => $candidate) {
                if (is_file($candidate)) {
                    $servePath = 'serve-category-image.php?disk=' . rawurlencode($disk) . '&file=' . rawurlencode($filename);
                    return '/' . ltrim($servePath, '/');
                }
            }
        }

        return '/' . ltrim($trimmed, '/');
    }
}

if (!function_exists('resolve_product_image_url')) {
    function resolve_product_image_url(?string $path): ?string
    {
        if ($path === null) {
            return null;
        }

        $trimmed = trim($path);
        if ($trimmed === '') {
            return null;
        }

        if (preg_match('/^https?:\/\//i', $trimmed) === 1) {
            return $trimmed;
        }

        $parsed = parse_url($trimmed);
        if ($parsed !== false && isset($parsed['path']) && str_ends_with($parsed['path'], 'serve-product-image.php')) {
            return $trimmed;
        }

        $projectRoot = dirname(__DIR__, 3);
        $relative = ltrim($trimmed, '/');
        $absolute = $projectRoot . '/' . $relative;

        if (is_file($absolute)) {
            return '/' . ltrim($trimmed, '/');
        }

        $filename = basename($relative);

        $validPatterns = [
            '/^[a-z0-9]+(?:-[a-z0-9]+)*-[0-9]{8}-[0-9]{6}(?:-[0-9]+)?\.(jpg|jpeg|png|gif|webp)$/i',
            '/^[a-z0-9]+(?:-[a-z0-9]+)*-[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}(?:-[0-9]+)?\.(jpg|jpeg|png|gif|webp)$/i',
            '/^[a-z0-9]+(?:-[a-z0-9]+)*-[0-9a-f]{16}(?:-[0-9]+)?\.(jpg|jpeg|png|gif|webp)$/i',
            '/^[a-z0-9]+(?:-[a-z0-9]+)*-[0-9]{8}-[0-9]{6}-[0-9a-f]{6}(?:-[0-9]+)?\.(jpg|jpeg|png|gif|webp)$/i',
            '/^[a-z0-9]+(?:-[a-z0-9]+)*-fallback-[0-9]+\.[0-9]+-[0-9a-f]+-[0-9]+\.(jpg|jpeg|png|gif|webp)$/i',
        ];

        $matchesNamingPattern = false;
        if ($filename !== '') {
            foreach ($validPatterns as $pattern) {
                if (preg_match($pattern, $filename) === 1) {
                    $matchesNamingPattern = true;
                    break;
                }
            }
        }

        if ($matchesNamingPattern) {
            $fallbacks = [
                'assets' => $projectRoot . '/assets/uploads/products/' . $filename,
                'storage' => $projectRoot . '/storage/uploads/products/' . $filename,
                'temp' => rtrim(sys_get_temp_dir(), DIRECTORY_SEPARATOR) . '/rcf-product-images/' . $filename,
            ];

            foreach ($fallbacks as $disk => $candidate) {
                if (is_file($candidate)) {
                    $servePath = 'serve-product-image.php?disk=' . rawurlencode((string) $disk) . '&file=' . rawurlencode($filename);
                    return '/' . ltrim($servePath, '/');
                }
            }
        }

        return '/' . ltrim($trimmed, '/');
    }
}

if (!function_exists('format_currency_value')) {
    /**
     * Format numeric values for currency display without introducing locale dependencies.
     */
    function format_currency_value($value, int $precision = 2): ?string
    {
        if ($value === null || $value === '') {
            return null;
        }

        if (!is_numeric($value)) {
            return null;
        }

        return number_format((float) $value, $precision, '.', '');
    }
}

if (!function_exists('build_product_card_payload')) {
    /**
     * Transform a raw product document into the structure expected by render_product_card().
     *
     * @param array<string, mixed> $product
     * @param array<string, mixed> $options
     */
    function build_product_card_payload(array $product, array $options = []): array
    {
        $currency = $options['currency'] ?? '$';
        $detailsPage = $options['details_page'] ?? 'shop-details.php';
        $defaultImage = $options['default_image'] ?? 'assets/images/grocery/01.jpg';

        if (isset($product['image']) && is_array($product['image']) && isset($product['price'])) {
            // Already in card format; merge with overrides.
            $card = $product;
            if (!isset($card['price']['currency'])) {
                $card['price']['currency'] = $currency;
            }
            if (isset($options['actions'])) {
                $card['actions'] = $options['actions'];
            }
            if (isset($options['details_button'])) {
                $card['details_button'] = $options['details_button'];
            }
            return $card;
        }

        $slug = $product['slug'] ?? null;
        $href = $product['href'] ?? ($slug ? $detailsPage . '?product=' . rawurlencode((string) $slug) : '#');

        $imageCandidates = [];
        if (isset($product['image_path']) && $product['image_path']) {
            $imageCandidates[] = $product['image_path'];
        }
        if (!empty($product['image_filename'])) {
            $imageCandidates[] = 'assets/uploads/products/' . $product['image_filename'];
        }
        if (isset($product['images']) && is_array($product['images'])) {
            foreach ($product['images'] as $maybeImage) {
                if (is_string($maybeImage) && $maybeImage !== '') {
                    $imageCandidates[] = $maybeImage;
                } elseif (is_array($maybeImage) && isset($maybeImage['path'])) {
                    $imageCandidates[] = $maybeImage['path'];
                }
            }
        }

        $resolvedImage = null;
        foreach ($imageCandidates as $candidate) {
            $candidateUrl = resolve_product_image_url(is_string($candidate) ? $candidate : null);
            if ($candidateUrl !== null) {
                $resolvedImage = $candidateUrl;
                break;
            }
        }

        if ($resolvedImage === null) {
            $resolvedImage = resolve_product_image_url($defaultImage) ?? $defaultImage;
        }

        $badge = null;
        $price = $product['price'] ?? null;
        $salePrice = $product['sale_price'] ?? null;
        if (is_numeric($price) && is_numeric($salePrice) && (float) $salePrice < (float) $price) {
            $discount = (float) $price > 0 ? (int) round((1 - ((float) $salePrice / (float) $price)) * 100) : null;
            $badge = [
                'prefix' => $discount !== null && $discount > 0 ? $discount . '%' : null,
                'text' => 'Off',
                'icon' => 'fa-solid fa-bookmark',
                'class' => 'badge',
            ];
        } elseif (!empty($product['featured'])) {
            $badge = [
                'text' => 'Featured',
                'class' => 'badge badge-featured',
            ];
        } elseif (isset($product['badge']) && is_array($product['badge'])) {
            $badge = $product['badge'];
        }

        $currentPrice = $salePrice !== null && $salePrice !== '' ? $salePrice : $price;
        $previousPrice = null;
        if ($salePrice !== null && $salePrice !== '' && is_numeric($salePrice) && is_numeric($price) && (float) $salePrice < (float) $price) {
            $previousPrice = $price;
        } elseif (isset($product['price_previous']) && $product['price_previous'] !== '') {
            $previousPrice = $product['price_previous'];
        }

        $stockQty = isset($product['stock_quantity']) && is_numeric($product['stock_quantity']) ? (int) $product['stock_quantity'] : null;
        $availability = $product['availability_label'] ?? $product['unit_label'] ?? null;
        if ($availability === null && $stockQty !== null) {
            if ($stockQty <= 0) {
                $availability = 'Out of stock';
            } elseif ($stockQty < 10) {
                $availability = 'Only ' . $stockQty . ' left';
            } else {
                $availability = 'In stock';
            }
        }

        $card = [
            'class' => $options['class'] ?? 'single-shopping-card-one tranding-product',
            'image' => [
                'src' => $resolvedImage,
                'alt' => $product['name'] ?? $product['title'] ?? 'Product image',
                'href' => $href,
            ],
            'badge' => $badge,
            'title' => $product['name'] ?? $product['title'] ?? 'Product',
            'href' => $href,
            'availability' => $availability,
            'price' => [
                'currency' => $currency,
                'current' => format_currency_value($currentPrice),
                'previous' => format_currency_value($previousPrice),
            ],
        ];

        if (isset($options['actions'])) {
            $card['actions'] = $options['actions'];
        }

        if (isset($options['details_button'])) {
            $card['details_button'] = $options['details_button'];
        }

        return $card;
    }
}

if (!function_exists('build_category_tile_payload')) {
    /**
     * Convert a category document into the simple structure expected by render_category_tile().
     */
    function build_category_tile_payload(array $category, array $options = []): array
    {
        $defaultImage = $options['default_image'] ?? 'assets/images/category/01.png';
        $targetPage = $options['target'] ?? 'shop-grid-sidebar.php';
        $queryParam = $options['query_param'] ?? 'category';
        $class = $options['class'] ?? 'single-category-one';

        $slug = $category['slug'] ?? null;
        $href = $category['href'] ?? ($slug ? ($targetPage . '?' . http_build_query([$queryParam => $slug])) : '#');

        $resolvedImage = null;
        if (isset($category['image']) && is_array($category['image']) && isset($category['image']['src'])) {
            $resolvedImage = $category['image']['src'];
        }

        if ($resolvedImage === null) {
            $candidate = $category['image_path'] ?? $category['icon'] ?? null;
            $resolvedImage = resolve_category_image_url(is_string($candidate) ? $candidate : null);
        }

        if ($resolvedImage === null) {
            $resolvedImage = resolve_category_image_url($defaultImage) ?? $defaultImage;
        }

        return [
            'href' => $href,
            'label' => $category['label'] ?? $category['name'] ?? 'Category',
            'class' => $class,
            'image' => $resolvedImage ? [
                'src' => $resolvedImage,
                'alt' => $category['name'] ?? $category['label'] ?? 'category',
            ] : null,
        ];
    }
}

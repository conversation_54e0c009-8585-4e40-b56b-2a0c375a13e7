<?php
// Robust path resolution for web server environment
$rootDir = null;

// Method 1: Use __DIR__ and go up one level
if (file_exists(__DIR__ . '/../bootstrap.php')) {
    $rootDir = __DIR__ . '/..';
}
// Method 2: Use dirname(__DIR__)
elseif (file_exists(dirname(__DIR__) . '/bootstrap.php')) {
    $rootDir = dirname(__DIR__);
}
// Method 3: Use realpath
elseif (file_exists(realpath(__DIR__ . '/../bootstrap.php'))) {
    $rootDir = realpath(__DIR__ . '/..');
}
// Method 4: Check if we're already in the right place
elseif (file_exists('./bootstrap.php')) {
    $rootDir = '.';
}
else {
    // Error handling
    http_response_code(500);
    die('Error: Cannot locate bootstrap.php. Please check file structure.');
}

require_once $rootDir . '/bootstrap.php';
require_once $rootDir . '/components/admin/index.php';
require_once $rootDir . '/components/admin/categories/index.php';

$repository = new CategoryRepository();
$service = new CategoryService($repository);
$statusOptions = $service->getStatusOptions();
$requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';

$performCategoryDeletion = static function (string $deleteId) use ($service) {
    if ($deleteId === '') {
        Flash::add('error', 'Category selection is invalid.');
        header('Location: categories.php');
        exit;
    }

    $result = $service->deleteCategory($deleteId);
    if ($result['success']) {
        Flash::add('success', $result['message'] ?? 'Category deleted successfully.');
    } else {
        $message = $result['message'] ?? ($result['errors']['general'] ?? 'Failed to delete category.');
        Flash::add('error', $message);
    }

    header('Location: categories.php');
    exit;
};

// Handle delete action via POST (primary flow)
if ($requestMethod === 'POST' && ($_POST['action'] ?? '') === 'delete') {
    $deleteId = isset($_POST['id']) ? trim((string) $_POST['id']) : '';
    $performCategoryDeletion($deleteId);
}

// Graceful fallback for legacy GET requests
if (isset($_GET['action']) && $_GET['action'] === 'delete' && isset($_GET['id'])) {
    $deleteId = trim((string) $_GET['id']);
    $performCategoryDeletion($deleteId);
}

$page = isset($_GET['page']) ? max(1, (int) $_GET['page']) : 1;
$perPage = 10;
$search = trim((string) ($_GET['search'] ?? ''));
$statusFilter = $_GET['status'] ?? '';
if ($statusFilter !== '' && !array_key_exists($statusFilter, $statusOptions)) {
    $statusFilter = '';
}

$pagination = $service->paginateCategories($page, $perPage, $search ?: null, $statusFilter ?: null);
$categories = $pagination['data'];
$totalPages = $pagination['total_pages'];
$currentPage = $pagination['current_page'];
$totalCount = $pagination['total'];

$flashMessages = Flash::consume();

$dashboardData = get_admin_dashboard_data();
foreach ($dashboardData['sidebar']['menu'] as &$menuItem) {
    if (($menuItem['href'] ?? '') === 'categories.php') {
        $menuItem['is_active'] = true;
    }
}
unset($menuItem);

if (!function_exists('format_category_date')) {
    function format_category_date($timestamp): string
    {
        return \RCF\Support\Formatting\DateNormalizer::formatForDisplay($timestamp, 'M d, Y g:i A');
    }
}

if (!function_exists('build_category_query')) {
    function build_category_query(array $overrides = []): string
    {
        $base = [
            'search' => $_GET['search'] ?? null,
            'status' => $_GET['status'] ?? null,
        ];
        $query = array_merge($base, $overrides);

        return http_build_query(array_filter($query, static fn ($value) => $value !== null && $value !== ''));
    }
}

if (!function_exists('category_status_label')) {
    function category_status_label(string $status, array $statusOptions): string
    {
        return $statusOptions[$status] ?? ucfirst(str_replace('_', ' ', $status));
    }
}

if (!function_exists('category_status_modifier')) {
    function category_status_modifier(string $status): string
    {
        $allowed = ['active', 'inactive', 'draft'];
        return in_array($status, $allowed, true) ? $status : 'inactive';
    }
}
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="description" content="rcf-Grocery-Store(e-Commerce) HTML Template: A sleek, responsive, and user-friendly HTML template designed for online grocery stores.">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="keywords" content="Grocery, Store, stores">
    <title>RC Furnishing Admin – Categories</title>
    <link rel="shortcut icon" type="image/x-icon" href="assets/images/fav.png">
    <link rel="stylesheet preload" href="assets/css/plugins.css" as="style">
    <link rel="stylesheet preload" href="https://cdn.datatables.net/1.10.15/css/jquery.dataTables.min.css" as="style">
    <link rel="stylesheet preload" href="assets/css/style.css" as="style">
    <style>
        .admin-table-card {
            padding: 24px;
            background: #ffffff;
            border-radius: 16px;
            box-shadow: 0 12px 30px rgba(35, 46, 60, 0.08);
        }

        .admin-table-card__header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 16px;
            flex-wrap: wrap;
        }

        .admin-table-card__heading {
            display: flex;
            flex-direction: column;
            gap: 8px;
            min-width: 0;
        }

        .admin-table-card__title {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
            color: #0f172a;
        }

        .admin-table-card__meta {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            color: #475569;
            font-size: 14px;
        }

        .admin-table-card__meta-item {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            line-height: 1.4;
        }

        .admin-table-card__cta {
            white-space: nowrap;
        }

        .admin-table-filters {
            display: grid;
            gap: 12px;
            margin-bottom: 20px;
            padding: 16px;
            grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
            align-items: center;
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 16px;
        }

        .admin-table-filters__control {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .admin-table-filters__control > input,
        .admin-table-filters__control > select,
        .admin-table-filters__control > .mySelect,
        .admin-table-filters__control > .nice-select {
            width: 100%;
        }

        .admin-table-filters__control input,
        .admin-table-filters__control select {
            padding: 12px 16px;
            border-radius: 12px;
            border: 1px solid #e2e8f0;
        }

        .admin-table-filters__actions {
            display: flex;
            gap: 8px;
            justify-content: flex-end;
            align-items: center;
            grid-column: 1 / -1;
        }

        .admin-table-filters__actions .admin-table__action-button {
            width: 120px;
            height: 36px;
        }

        .admin-table-alerts {
            margin-bottom: 20px;
        }

        .admin-table-alert {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 12px;
            font-weight: 500;
        }

        .admin-table-alert--success {
            color: #1b5e20;
            background: #e8f5e9;
        }

        .admin-table-alert--error {
            color: #b42318;
            background: #fee4e2;
        }

        .admin-table {
            width: 100%;
            border-collapse: collapse;
        }

        .admin-table thead th,
        .admin-table tbody td {
            white-space: normal;
            vertical-align: middle;
            padding: 14px 18px;
        }

        .admin-table thead th {
            color: #475569;
            font-weight: 600;
            font-size: 14px;
            border-bottom: 1px solid #e2e8f0;
        }

        .admin-table tbody tr {
            border-bottom: 1px solid #f1f5f9;
        }

        .admin-table tbody tr:last-child {
            border-bottom: none;
        }

        .admin-table__empty td {
            text-align: center;
            padding: 32px;
            color: #64748b;
        }

        .admin-table__entry {
            display: flex;
            align-items: center;
            gap: 16px;
        }

        .admin-table__thumb {
            width: 56px;
            height: 56px;
            border-radius: 12px;
            overflow: hidden;
            background: #f1f5f9;
            flex-shrink: 0;
        }

        .admin-table__thumb img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .admin-table__entry-body {
            display: flex;
            flex-direction: column;
            gap: 4px;
        }

        .admin-table__entry-sub {
            color: #64748b;
            font-size: 14px;
        }

        .admin-table__pricing {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .admin-table__pricing del {
            color: #94a3b8;
            font-size: 14px;
        }

        .admin-table__status-badge {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            padding: 4px 10px;
            border-radius: 999px;
            font-size: 12px;
            font-weight: 600;
            text-transform: capitalize;
        }

        .admin-table__status-badge--active {
            background: #dcfce7;
            color: #166534;
        }

        .admin-table__status-badge--inactive {
            background: #fee2e2;
            color: #b91c1c;
        }

        .admin-table__status-badge--draft {
            background: #fef3c7;
            color: #92400e;
        }

        .admin-table__status-badge--out_of_stock {
            background: #ede9fe;
            color: #5b21b6;
        }

        .admin-table__cell--actions {
            text-align: right;
        }

        .admin-table__actions {
            display: flex;
            justify-content: flex-end;
            align-items: center;
            gap: 8px;
            flex-wrap: wrap;
        }

        .admin-table__action-button {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 6px;
            width: 124px;
            height: 38px;
            border-radius: 12px;
            border: 1px solid transparent;
            font-size: 13px;
            font-weight: 600;
            line-height: 1;
            text-decoration: none;
            cursor: pointer;
            background: #2563eb;
            color: #ffffff;
            transition: background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease, box-shadow 0.2s ease;
            font-family: inherit;
            -webkit-appearance: none;
            appearance: none;
        }

        .admin-table__action-button:hover,
        .admin-table__action-button:focus-visible {
            background: #1d4ed8;
            border-color: #1d4ed8;
            color: #ffffff;
        }

        .admin-table__action-button:focus-visible {
            outline: none;
            box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.3);
        }

        .admin-table__action-button--secondary {
            background: #eff6ff;
            color: #1d4ed8;
            border-color: #bfdbfe;
        }

        .admin-table__action-button--secondary:hover,
        .admin-table__action-button--secondary:focus-visible {
            background: #1d4ed8;
            color: #ffffff;
            border-color: #1d4ed8;
        }

        .admin-table__action-button--danger {
            background: #fee2e2;
            color: #b91c1c;
            border-color: #fecaca;
        }

        .admin-table__action-button--danger:hover,
        .admin-table__action-button--danger:focus-visible {
            background: #dc2626;
            color: #ffffff;
            border-color: #dc2626;
        }

        .admin-table__action-icon {
            display: inline-flex;
            align-items: center;
            font-size: 14px;
        }

        .admin-table__action-label {
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .admin-table-pagination {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 20px;
            flex-wrap: wrap;
            gap: 12px;
            color: #475569;
            font-size: 14px;
        }

        .admin-table-pagination__controls {
            display: flex;
            gap: 8px;
        }

        @media (max-width: 991px) {
            .admin-table-filters {
                grid-template-columns: 1fr;
            }

            .admin-table-filters__actions {
                justify-content: flex-start;
            }
        }

        @media (max-width: 767px) {
            .admin-table thead {
                display: none;
            }

            .admin-table tbody tr {
                display: block;
                border: 1px solid #edf2f7;
                border-radius: 12px;
                padding: 16px;
                margin-bottom: 16px;
            }

            .admin-table tbody tr td {
                display: flex;
                flex-direction: column;
                gap: 6px;
                padding: 8px 0;
                border: none;
            }

            .admin-table tbody tr td::before {
                content: attr(data-label);
                font-weight: 600;
                color: #64748b;
            }

            .admin-table__cell--actions {
                text-align: left;
            }

            .admin-table__actions {
                flex-direction: column;
                align-items: stretch;
            }

            .admin-table__action-button {
                width: 100%;
                justify-content: center;
            }
        }
    </style>
</head>
<body>
    <div class="rcf_dashboard">
        <?php render_admin_sidebar($dashboardData['sidebar']); ?>
        <div class="right-area-body-content">
            <?php render_admin_header($dashboardData['header']); ?>
            <div class="body-root-inner">
                <div class="transection">
                    <div class="row g-5 align-items-start">
                        <div class="col-12">
                            <div class="admin-table-card mb--20">
                                <div class="admin-table-card__header">
                                    <div class="admin-table-card__heading">
                                        <h3 class="admin-table-card__title">Categories</h3>
                                        <div class="admin-table-card__meta">
                                            <span class="admin-table-card__meta-item">Total categories: <?= number_format($totalCount); ?></span>
                                            <?php if ($search !== ''): ?>
                                                <span class="admin-table-card__meta-item">Search: “<?= htmlspecialchars($search, ENT_QUOTES, 'UTF-8'); ?>”</span>
                                            <?php endif; ?>
        
                                            <?php if ($statusFilter !== ''): ?>
                                                <span class="admin-table-card__meta-item">Status: <?= htmlspecialchars(category_status_label($statusFilter, $statusOptions), ENT_QUOTES, 'UTF-8'); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                    <a href="add-category.php" class="rts-btn btn-primary admin-table-card__cta">+ Add Category</a>
                                </div>

                
                                <form method="get" class="admin-table-filters">
                                    <div class="admin-table-filters__control">
                                        <input type="search" name="search" value="<?= htmlspecialchars($search, ENT_QUOTES, 'UTF-8'); ?>" placeholder="Search categories" aria-label="Search categories">
                                    </div>
                                    <div class="admin-table-filters__control">
                                        <select name="status" class="mySelect" aria-label="Filter by status">
                                            <option value="" data-display="All Statuses">All Statuses</option>
                                            <?php foreach ($statusOptions as $value => $label): ?>
                                                <option value="<?= htmlspecialchars($value, ENT_QUOTES, 'UTF-8'); ?>" data-display="<?= htmlspecialchars($label, ENT_QUOTES, 'UTF-8'); ?>"<?= $statusFilter === $value ? ' selected' : ''; ?>><?= htmlspecialchars($label, ENT_QUOTES, 'UTF-8'); ?></option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <div class="admin-table-filters__actions">
                                        <button type="submit" class="admin-table__action-button">Apply</button>
                                        <?php if ($search !== '' || $statusFilter !== ''): ?>
                                            <a class="admin-table__action-button admin-table__action-button--secondary" href="categories.php">Reset</a>
                                        <?php endif; ?>
                                    </div>
                                </form>

                                <?php if (!empty($flashMessages)): ?>
                                    <div class="admin-table-alerts" role="status" aria-live="polite">
                                        <?php foreach ($flashMessages as $type => $messages): ?>
                                            <?php foreach ($messages as $message): ?>
                                                <div class="admin-table-alert admin-table-alert--<?= $type === 'success' ? 'success' : 'error'; ?>">
                                                    <?= htmlspecialchars($message, ENT_QUOTES, 'UTF-8'); ?>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>

                                <div class="table-responsive">
                                    <table class="table table-hover admin-table">
                                        <thead>
                                            <tr>
                                                <th scope="col" style="min-width: 160px;">Name</th>
                                                <th scope="col">Description</th>
                                                <th scope="col" style="min-width: 120px;">Status</th>
                                                <th scope="col" style="min-width: 140px;">Created</th>
                                                <th scope="col" style="min-width: 120px;" class="text-end">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php if (empty($categories)): ?>
                                                <tr class="admin-table__empty">
                                                    <td colspan="5">No categories found. Start by creating a new one.</td>
                                                </tr>
                                            <?php else: ?>
                                                <?php foreach ($categories as $category): ?>
                                                    <tr>
                                                        <td data-label="Name">
                                                            <strong><?= htmlspecialchars($category['name'], ENT_QUOTES, 'UTF-8'); ?></strong>
                                                        </td>
                                                        <td data-label="Description"><?= htmlspecialchars($category['description'] ?? '—', ENT_QUOTES, 'UTF-8'); ?></td>
                                                        <td data-label="Status">
                                                            <?php $categoryStatus = (string) ($category['status'] ?? 'inactive'); ?>
                                                            <span class="admin-table__status-badge admin-table__status-badge--<?= htmlspecialchars(category_status_modifier($categoryStatus), ENT_QUOTES, 'UTF-8'); ?>">
                                                                <?= htmlspecialchars(category_status_label($categoryStatus, $statusOptions), ENT_QUOTES, 'UTF-8'); ?>
                                                            </span>
                                                        </td>
                                                        <td data-label="Created"><?= htmlspecialchars(format_category_date($category['created_at'] ?? ''), ENT_QUOTES, 'UTF-8'); ?></td>
                                                        <td data-label="Actions" class="admin-table__cell admin-table__cell--actions">
                                                            <div class="admin-table__actions" role="group" aria-label="Manage category <?= htmlspecialchars($category['name'], ENT_QUOTES, 'UTF-8'); ?>">
                                                                <a class="admin-table__action-button admin-table__action-button--secondary" href="add-category.php?action=edit&id=<?= rawurlencode($category['id']); ?>">
                                                                    <i class="fa-light fa-pen-to-square admin-table__action-icon" aria-hidden="true"></i>
                                                                    <span class="admin-table__action-label">Edit</span>
                                                                </a>
                                                                <button type="button" class="admin-table__action-button admin-table__action-button--danger category-delete-button" data-category-id="<?= htmlspecialchars($category['id'], ENT_QUOTES, 'UTF-8'); ?>" data-category-name="<?= htmlspecialchars($category['name'], ENT_QUOTES, 'UTF-8'); ?>" aria-label="Delete category <?= htmlspecialchars($category['name'], ENT_QUOTES, 'UTF-8'); ?>">
                                                                    <i class="fa-light fa-trash-can admin-table__action-icon" aria-hidden="true"></i>
                                                                    <span class="admin-table__action-label">Delete</span>
                                                                </button>
                                                            </div>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            <?php endif; ?>
                                        </tbody>
                                    </table>
                                </div>

                                <?php if ($totalPages > 1): ?>
                                    <div class="admin-table-pagination">
                                        <div class="admin-table-pagination__info">
                                            Page <?= $currentPage; ?> of <?= $totalPages; ?>
                                        </div>
                                        <div class="admin-table-pagination__controls">
                                            <?php
                                                $prevPage = max(1, $currentPage - 1);
                                                $nextPage = min($totalPages, $currentPage + 1);
                                                $prevQuery = build_category_query(['page' => $prevPage]);
                                                $nextQuery = build_category_query(['page' => $nextPage]);
                                                $prevHref = 'categories.php' . ($prevQuery !== '' ? '?' . $prevQuery : '');
                                                $nextHref = 'categories.php' . ($nextQuery !== '' ? '?' . $nextQuery : '');
                                            ?>
                                            <a class="admin-table__action-button admin-table__action-button--secondary" href="<?= htmlspecialchars($prevHref, ENT_QUOTES, 'UTF-8'); ?>"<?= $currentPage === 1 ? ' aria-disabled="true" style="pointer-events:none; opacity:0.5;"' : ''; ?>>Prev</a>
                                            <a class="admin-table__action-button admin-table__action-button--secondary" href="<?= htmlspecialchars($nextHref, ENT_QUOTES, 'UTF-8'); ?>"<?= $currentPage === $totalPages ? ' aria-disabled="true" style="pointer-events:none; opacity:0.5;"' : ''; ?>>Next</a>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                <form method="post" id="delete-category-form" style="display:none;">
                    <input type="hidden" name="action" value="delete">
                    <input type="hidden" name="id" id="delete-category-id" value="">
                </form>
                <?php render_admin_footer($dashboardData['footer']); ?>
            </div>
        </div>
    </div>
    <?php render_admin_overlays(); ?>
    <script src="assets/js/plugins.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.27.0/dist/apexcharts.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.15/js/jquery.dataTables.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var deleteButtons = document.querySelectorAll('.category-delete-button');
            var deleteForm = document.getElementById('delete-category-form');
            var deleteIdField = document.getElementById('delete-category-id');

            function submitDeletion(categoryId, categoryName) {
                if (!deleteForm || !deleteIdField) {
                    return;
                }

                var message = 'Are you sure you want to delete the category "' + categoryName + '"? This action cannot be undone.';
                if (!window.confirm(message)) {
                    return;
                }

                deleteIdField.value = categoryId;
                deleteForm.submit();
            }

            deleteButtons.forEach(function (button) {
                button.addEventListener('click', function (event) {
                    event.preventDefault();
                    var categoryId = button.getAttribute('data-category-id');
                    var categoryName = button.getAttribute('data-category-name') || 'this category';
                    if (!categoryId) {
                        return;
                    }
                    submitDeletion(categoryId, categoryName);
                });
            });
        });
    </script>
</body>
</html>

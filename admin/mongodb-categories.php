<?php
/**
 * MongoDB Categories Management
 * 
 * Admin interface for managing categories with MongoDB
 */

require_once __DIR__ . '/../bootstrap.php';

$pageTitle = 'Categories Management - MongoDB';
$message = '';
$messageType = '';

$requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
$databaseAvailable = is_database_available();
$usingMock = false;

try {
    $usingMock = getMongoConnection()->isUsingMock();
} catch (Throwable $exception) {
    $usingMock = false;
}

// Handle form submissions
if ($requestMethod === 'POST') {
    if (!$databaseAvailable) {
        $message = 'Database connection is currently unavailable. Please try again later.';
        $messageType = 'error';
    } else {
        $repo = getCategoryRepository();

        if (isset($_POST['action'])) {
            switch ($_POST['action']) {
                case 'create':
                    try {
                        $categoryData = [
                            'name' => trim($_POST['name'] ?? ''),
                            'slug' => trim($_POST['slug'] ?? ''),
                            'description' => trim($_POST['description'] ?? ''),
                            'status' => $_POST['status'] ?? 'active',
                        ];

                        $categoryId = $repo->createCategory($categoryData);
                        if ($categoryId) {
                            $message = 'Category created successfully!';
                            $messageType = 'success';
                        } else {
                            $message = 'Failed to create category.';
                            $messageType = 'error';
                        }
                    } catch (Exception $exception) {
                        $message = 'Error: ' . $exception->getMessage();
                        $messageType = 'error';
                    }
                    break;

                case 'update':
                    try {
                        $categoryData = [
                            'name' => trim($_POST['name'] ?? ''),
                            'description' => trim($_POST['description'] ?? ''),
                            'status' => $_POST['status'] ?? 'active',
                        ];

                        if ($repo->updateById($_POST['category_id'] ?? '', $categoryData)) {
                            $message = 'Category updated successfully!';
                            $messageType = 'success';
                        } else {
                            $message = 'Failed to update category.';
                            $messageType = 'error';
                        }
                    } catch (Exception $exception) {
                        $message = 'Error: ' . $exception->getMessage();
                        $messageType = 'error';
                    }
                    break;

                case 'delete':
                    try {
                        if ($repo->deleteById($_POST['category_id'] ?? '')) {
                            $message = 'Category deleted successfully!';
                            $messageType = 'success';
                        } else {
                            $message = 'Failed to delete category.';
                            $messageType = 'error';
                        }
                    } catch (Exception $exception) {
                        $message = 'Error: ' . $exception->getMessage();
                        $messageType = 'error';
                    }
                    break;

                default:
                    $message = 'Unknown action requested.';
                    $messageType = 'error';
                    break;
            }
        }
    }
}

// Get categories
$categories = [];
$stats = [];
if ($databaseAvailable) {
    $repo = getCategoryRepository();
    $categories = $repo->find([], ['sort' => ['created_at' => -1]]);
    $stats = $repo->getStatistics();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= htmlspecialchars($pageTitle) ?></title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .container { max-width: 1200px; margin: 0 auto; }
        .message { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #e3f2fd; color: #0d47a1; border: 1px solid #90caf9; }
        .form-group { margin: 15px 0; }
        .form-group label { display: block; margin-bottom: 5px; font-weight: bold; }
        .form-group input, .form-group select, .form-group textarea { 
            width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px; 
        }
        .btn { padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; margin: 5px; }
        .btn:hover { background: #0056b3; }
        .btn-danger { background: #dc3545; }
        .btn-danger:hover { background: #c82333; }
        .btn-success { background: #28a745; }
        .btn-success:hover { background: #218838; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background: #f8f9fa; }
        .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin: 20px 0; }
        .stat-card { background: #f8f9fa; padding: 20px; border-radius: 5px; text-align: center; }
        .stat-number { font-size: 2em; font-weight: bold; color: #007bff; }
        .modal { display: none; position: fixed; z-index: 1000; left: 0; top: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); }
        .modal-content { background: white; margin: 15% auto; padding: 20px; border-radius: 5px; width: 80%; max-width: 500px; }
        .close { color: #aaa; float: right; font-size: 28px; font-weight: bold; cursor: pointer; }
        .close:hover { color: black; }
    </style>
</head>
<body>
    <div class="container">
        <h1>Categories Management (MongoDB)</h1>
        
        <?php if (!$databaseAvailable): ?>
            <div class="message warning">
                <strong>Warning:</strong> MongoDB is not available. Please check your connection and run the migration.
                <br><a href="../mongodb-demo.php">View MongoDB Demo Page</a>
            </div>
        <?php else: ?>

            <?php if ($usingMock): ?>
                <div class="message info">
                    <strong>Notice:</strong> Running against the mock MongoDB store. Data persists to JSON files in <code>storage/mock_mongodb</code>.
                </div>
            <?php endif; ?>

            <?php if ($message): ?>
                <div class="message <?= $messageType ?>">
                    <?= htmlspecialchars($message) ?>
                </div>
            <?php endif; ?>
            
            <!-- Statistics -->
            <div class="stats">
                <div class="stat-card">
                    <div class="stat-number"><?= $stats['active'] ?? 0 ?></div>
                    <div>Active Categories</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $stats['inactive'] ?? 0 ?></div>
                    <div>Inactive Categories</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= $stats['draft'] ?? 0 ?></div>
                    <div>Draft Categories</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number"><?= array_sum($stats) ?></div>
                    <div>Total Categories</div>
                </div>
            </div>
            
            <!-- Add Category Form -->
            <div style="background: #f8f9fa; padding: 20px; border-radius: 5px; margin: 20px 0;">
                <h3>Add New Category</h3>
                <form method="POST">
                    <input type="hidden" name="action" value="create">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div class="form-group">
                            <label>Name:</label>
                            <input type="text" name="name" required>
                        </div>
                        <div class="form-group">
                            <label>Slug:</label>
                            <input type="text" name="slug" required pattern="[a-z0-9-]+" title="Only lowercase letters, numbers, and hyphens">
                        </div>
                    </div>
                    <div class="form-group">
                        <label>Description:</label>
                        <textarea name="description" rows="3"></textarea>
                    </div>
                    <div class="form-group">
                        <label>Status:</label>
                        <select name="status">
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                            <option value="draft">Draft</option>
                        </select>
                    </div>
                    <button type="submit" class="btn btn-success">Add Category</button>
                </form>
            </div>
            
            <!-- Categories List -->
            <h3>Existing Categories</h3>
            <table>
                <thead>
                    <tr>
                        <th>Name</th>
                        <th>Slug</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($categories as $category): ?>
                    <tr>
                        <td><?= htmlspecialchars($category['name'] ?? '', ENT_QUOTES, 'UTF-8') ?></td>
                        <td><?= htmlspecialchars($category['slug'] ?? '', ENT_QUOTES, 'UTF-8') ?></td>
                        <td>
                            <span style="padding: 4px 8px; border-radius: 3px; font-size: 0.8em; 
                                background: <?= ($category['status'] ?? '') === 'active' ? '#d4edda' : (($category['status'] ?? '') === 'inactive' ? '#f8d7da' : '#fff3cd') ?>;">
                                <?= htmlspecialchars(ucfirst($category['status'] ?? 'unknown'), ENT_QUOTES, 'UTF-8') ?>
                            </span>
                        </td>
                        <td>
                            <?php
                                echo htmlspecialchars(
                                    \RCF\Support\Formatting\DateNormalizer::formatForDisplay($category['created_at'] ?? null, 'M d, Y g:i A'),
                                    ENT_QUOTES,
                                    'UTF-8'
                                );
                            ?>
                        </td>
                        <td>
                            <button onclick="editCategory('<?= htmlspecialchars($category['id'] ?? '', ENT_QUOTES, 'UTF-8') ?>', '<?= htmlspecialchars($category['name'] ?? '', ENT_QUOTES, 'UTF-8') ?>', '<?= htmlspecialchars($category['description'] ?? '', ENT_QUOTES, 'UTF-8') ?>', '<?= htmlspecialchars($category['status'] ?? '', ENT_QUOTES, 'UTF-8') ?>')" class="btn">Edit</button>
                            <button onclick="deleteCategory('<?= htmlspecialchars($category['id'] ?? '', ENT_QUOTES, 'UTF-8') ?>', '<?= htmlspecialchars($category['name'] ?? '', ENT_QUOTES, 'UTF-8') ?>')" class="btn btn-danger">Delete</button>
                        </td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            
        <?php endif; ?>
        
        <p><a href="../mongodb-demo.php" class="btn">← Back to Demo</a></p>
    </div>
    
    <!-- Edit Modal -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <span class="close" onclick="closeModal()">&times;</span>
            <h3>Edit Category</h3>
            <form method="POST">
                <input type="hidden" name="action" value="update">
                <input type="hidden" name="category_id" id="edit_category_id">
                <div class="form-group">
                    <label>Name:</label>
                    <input type="text" name="name" id="edit_name" required>
                </div>
                <div class="form-group">
                    <label>Description:</label>
                    <textarea name="description" id="edit_description" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label>Status:</label>
                    <select name="status" id="edit_status">
                        <option value="active">Active</option>
                        <option value="inactive">Inactive</option>
                        <option value="draft">Draft</option>
                    </select>
                </div>
                <button type="submit" class="btn btn-success">Update Category</button>
            </form>
        </div>
    </div>
    
    <script>
        function editCategory(id, name, description, status) {
            document.getElementById('edit_category_id').value = id;
            document.getElementById('edit_name').value = name;
            document.getElementById('edit_description').value = description;
            document.getElementById('edit_status').value = status;
            document.getElementById('editModal').style.display = 'block';
        }
        
        function deleteCategory(id, name) {
            if (confirm('Are you sure you want to delete "' + name + '"?')) {
                const form = document.createElement('form');
                form.method = 'POST';
                form.innerHTML = '<input type="hidden" name="action" value="delete"><input type="hidden" name="category_id" value="' + id + '">';
                document.body.appendChild(form);
                form.submit();
            }
        }
        
        function closeModal() {
            document.getElementById('editModal').style.display = 'none';
        }
        
        window.onclick = function(event) {
            const modal = document.getElementById('editModal');
            if (event.target === modal) {
                modal.style.display = 'none';
            }
        }
    </script>
</body>
</html>

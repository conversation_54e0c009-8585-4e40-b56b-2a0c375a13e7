<?php
require_once __DIR__ . '/../bootstrap.php';
require_once __DIR__ . '/../components/admin/index.php';
require_once __DIR__ . '/../components/admin/categories/index.php';
require_once __DIR__ . '/../components/admin/products/index.php';

$categoryRepository = new CategoryRepository();
$categoryService = new CategoryService($categoryRepository);
$categories = $categoryService->listAllCategories();
$categoryLookup = [];
foreach ($categories as $category) {
    $categoryLookup[$category['id']] = $category['name'];
}

$productRepository = getProductRepository();
$productService = new ProductService($productRepository);
$statusOptions = $productService->getStatusOptions();

$formMode = 'create';
$editingId = null;
$errors = [];
$formData = [
    'name' => '',
    'description' => '',
    'price' => '',
    'sale_price' => '',
    'stock_quantity' => '',
    'category_id' => '',
    'tags' => '',
    'status' => 'active',
    'featured' => false,
    'weight' => '',
    'length' => '',
    'width' => '',
    'height' => '',
    'sku' => '',
    'image_path' => '',
];

$requestMethod = $_SERVER['REQUEST_METHOD'] ?? 'GET';
$action = $_GET['action'] ?? null;

if ($requestMethod === 'POST') {
    $formMode = ($_POST['form_action'] ?? 'create') === 'update' ? 'edit' : 'create';
    $editingId = isset($_POST['product_id']) ? trim((string) $_POST['product_id']) : null;
    if ($editingId === '') {
        $editingId = null;
    }

    $formData = [
        'name' => $_POST['name'] ?? '',
        'description' => $_POST['description'] ?? '',
        'price' => $_POST['price'] ?? '',
        'sale_price' => $_POST['sale_price'] ?? '',
        'stock_quantity' => $_POST['stock_quantity'] ?? '',
        'category_id' => $_POST['category_id'] ?? '',
        'tags' => $_POST['tags'] ?? '',
        'status' => $_POST['status'] ?? 'active',
        'featured' => isset($_POST['featured']) && in_array($_POST['featured'], ['1', 'on', 'true'], true),
        'weight' => $_POST['weight'] ?? '',
        'length' => $_POST['length'] ?? '',
        'width' => $_POST['width'] ?? '',
        'height' => $_POST['height'] ?? '',
        'sku' => $_POST['sku'] ?? '',
        'image_path' => $_POST['existing_image_path'] ?? '',
    ];

    if (!array_key_exists($formData['status'], $statusOptions)) {
        $formData['status'] = 'active';
    }

    if ($formMode === 'edit' && !$editingId) {
        Flash::add('error', 'Product selection is invalid.');
        header('Location: product-list.php');
        exit;
    }

    $imageUpload = $_FILES['product_image'] ?? null;

    if ($formMode === 'create') {
        $result = $productService->createProduct($formData, $imageUpload);
    } else {
        $result = $productService->updateProduct($editingId, $formData, $imageUpload);
    }

    if ($result['success']) {
        $successMessage = $result['message'] ?? ($formMode === 'edit' ? 'Product updated successfully.' : 'Product created successfully.');
        Flash::add('success', $successMessage);
        header('Location: product-list.php');
        exit;
    }

    $errors = $result['errors'] ?? [];
    if (!empty($result['errors']['general'])) {
        Flash::add('error', $result['errors']['general']);
    }
} elseif ($action === 'edit') {
    $editingId = isset($_GET['id']) ? trim((string) $_GET['id']) : null;
    if ($editingId === '') {
        $editingId = null;
    }

    if (!$editingId) {
        Flash::add('error', 'Please choose a product to edit.');
        header('Location: product-list.php');
        exit;
    }

    $product = $productService->getProduct($editingId);
    if ($product === null) {
        Flash::add('error', 'The requested product could not be found.');
        header('Location: product-list.php');
        exit;
    }

    $formMode = 'edit';
    $formData = [
        'name' => $product['name'] ?? '',
        'description' => $product['description'] ?? '',
        'price' => isset($product['price']) ? (string) $product['price'] : '',
        'sale_price' => isset($product['sale_price']) && $product['sale_price'] !== null ? (string) $product['sale_price'] : '',
        'stock_quantity' => isset($product['stock_quantity']) ? (string) $product['stock_quantity'] : '',
        'category_id' => $product['category_id'] ?? '',
        'tags' => !empty($product['tags']) ? implode(', ', $product['tags']) : '',
        'status' => $product['status'] ?? 'active',
        'featured' => !empty($product['featured']),
        'weight' => isset($product['weight']) && $product['weight'] !== null ? (string) $product['weight'] : '',
        'length' => isset($product['length']) && $product['length'] !== null ? (string) $product['length'] : '',
        'width' => isset($product['width']) && $product['width'] !== null ? (string) $product['width'] : '',
        'height' => isset($product['height']) && $product['height'] !== null ? (string) $product['height'] : '',
        'sku' => $product['sku'] ?? '',
        'image_path' => $product['image_path'] ?? '',
    ];

    if (!array_key_exists($formData['status'], $statusOptions)) {
        $formData['status'] = 'active';
    }
}

$flashMessages = Flash::consume();

$dashboardData = get_admin_dashboard_data();
foreach ($dashboardData['sidebar']['menu'] as &$menuItem) {
    if (($menuItem['href'] ?? '') === 'add-product.php') {
        $menuItem['is_active'] = true;
    }
}
unset($menuItem);

$pageTitle = $formMode === 'edit' ? 'Edit Product' : 'Add Product';
$formDescription = $formMode === 'edit'
    ? 'Update the product details below to keep your catalogue accurate and informative.'
    : 'Add a new product to expand your catalogue and keep your store fresh.';

$placeholderProductImage = 'assets/images/grocery/16.png';
$currentProductImage = resolve_product_image_url($formData['image_path'] ?? null) ?? $placeholderProductImage;
$imageUploadMessage = $formData['image_path'] !== ''
    ? 'Drag and drop or browse to replace the image.'
    : 'Drag and drop image or click browse to upload.';

$defaultPreviewName = 'New product';
$defaultPreviewDescription = 'Add a short description to highlight what makes this product special.';
$defaultPreviewCategory = 'Unassigned';
$previewName = $formData['name'] !== '' ? $formData['name'] : $defaultPreviewName;
$previewDescription = $formData['description'] !== '' ? $formData['description'] : $defaultPreviewDescription;
$previewCategory = $formData['category_id'] !== '' && isset($categoryLookup[$formData['category_id']])
    ? $categoryLookup[$formData['category_id']]
    : $defaultPreviewCategory;
$previewStatusLabel = $statusOptions[$formData['status']] ?? ucfirst($formData['status']);
$previewPriceLabel = $formData['price'] !== '' ? '$' . number_format((float) $formData['price'], 2) : '$0.00';
$previewSalePriceLabel = $formData['sale_price'] !== '' ? '$' . number_format((float) $formData['sale_price'], 2) : null;
$previewStockLabel = $formData['stock_quantity'] !== '' ? (int) $formData['stock_quantity'] : 0;

$statusModifier = in_array($formData['status'], array_keys($statusOptions), true) ? $formData['status'] : 'inactive';
$productFormMode = $formMode;

?>
<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="description" content="rcf-Grocery-Store(e-Commerce) HTML Template: A sleek, responsive, and user-friendly HTML template designed for online grocery stores.">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="keywords" content="Grocery, Store, stores">
        <title>RC Furnishing Admin – <?= htmlspecialchars($pageTitle, ENT_QUOTES, 'UTF-8'); ?></title>
        <link rel="shortcut icon" type="image/x-icon" href="assets/images/fav.png">
        <link rel="stylesheet preload" href="assets/css/plugins.css" as="style">
        <link rel="stylesheet preload" href="https://cdn.datatables.net/1.10.15/css/jquery.dataTables.min.css" as="style">
        <link rel="stylesheet preload" href="assets/css/style.css" as="style">
        <style>
            .product-form-page .table-product-select {
                padding: 40px;
                background: #ffffff;
                border-radius: 24px;
                box-shadow: 0 24px 60px rgba(15, 23, 42, 0.08);
            }

            .product-form-page .right-collups-area-top {
                display: flex;
                align-items: flex-start;
                justify-content: space-between;
                gap: 24px;
                margin-bottom: 32px;
            }

            .product-form-page .right-collups-area-top .title {
                font-size: 34px;
                font-weight: 600;
                margin-bottom: 8px;
                color: #0f172a;
            }

            .product-form-page__subtitle {
                display: inline-flex;
                align-items: center;
                gap: 8px;
                font-size: 13px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.08em;
                color: #6366f1;
            }

            .product-form-page .right-collups-area-top p {
                margin: 0;
                color: #64748b;
                max-width: 520px;
                line-height: 1.6;
            }

            .product-form-page__back {
                white-space: nowrap;
                display: inline-flex;
                align-items: center;
                gap: 8px;
            }

            .product-form-page__alerts {
                margin-top: 0;
            }

            .product-form-page__alert {
                padding: 12px 16px;
                border-radius: 12px;
                font-weight: 500;
                margin-bottom: 12px;
            }

            .product-form-page__alert--success {
                color: #0f5132;
                background: #d1f2e1;
            }

            .product-form-page__alert--error {
                color: #b42318;
                background: #fee4e2;
            }

            .product-form {
                display: flex;
                flex-direction: column;
                gap: 28px;
            }

            .product-form-page__layout {
                display: grid;
                grid-template-columns: minmax(0, 1fr) minmax(280px, 320px);
                gap: 32px;
                align-items: flex-start;
            }

            .product-form-page__main {
                display: flex;
                flex-direction: column;
                gap: 28px;
            }

            .product-form-section {
                background: #f8fafc;
                border: 1px solid #e2e8f0;
                border-radius: 18px;
                padding: 28px 32px;
                display: flex;
                flex-direction: column;
                gap: 24px;
            }

            .product-form-section__header {
                display: flex;
                flex-direction: column;
                gap: 6px;
            }

            .product-form-section__kicker {
                font-size: 12px;
                font-weight: 600;
                text-transform: uppercase;
                letter-spacing: 0.08em;
                color: #4f46e5;
            }

            .product-form-section__title {
                font-size: 22px;
                font-weight: 600;
                margin: 0;
                color: #0f172a;
            }

            .product-form-section__description {
                margin: 0;
                color: #64748b;
                max-width: 560px;
            }

            .product-form-section__grid {
                display: grid;
                gap: 20px;
                grid-template-columns: repeat(auto-fit, minmax(240px, 1fr));
            }

            .product-form-section__grid--single {
                grid-template-columns: 1fr;
            }

            .product-form-page .single-input {
                display: flex;
                flex-direction: column;
                gap: 12px;
                margin: 0;
            }

            .product-form-page .single-input label {
                font-weight: 600;
                color: #0f172a;
            }

            .product-form-page .single-input input,
            .product-form-page .single-input textarea,
            .product-form-page .single-input select {
                width: 100%;
                border-radius: 12px;
            }

            .product-form-page textarea {
                min-height: 160px;
                resize: vertical;
            }

            .product-form-page .single-input input.error,
            .product-form-page .single-input textarea.error,
            .product-form-page .single-input select.error {
                border-color: #b42318;
                box-shadow: 0 0 0 2px rgba(180, 35, 24, 0.1);
            }

            .product-form-page .field-error {
                display: block;
                margin-top: -4px;
                font-size: 12px;
                color: #b42318;
            }

            .product-form-page .character-counter {
                display: block;
                margin-top: 4px;
                font-size: 12px;
                color: #94a3b8;
                text-align: right;
            }

            .product-form-page__helper {
                display: block;
                font-size: 12px;
                color: #94a3b8;
            }

            .product-image-upload__wrapper {
                display: grid;
                grid-template-columns: minmax(0, 1fr) minmax(0, 220px);
                gap: 24px;
                align-items: stretch;
            }

            .product-image-upload__dropzone {
                border: 1px dashed #d0d8e7;
                border-radius: 18px;
                background: #f8fafc;
                padding: 24px;
                display: flex;
                align-items: center;
                justify-content: center;
                text-align: center;
                cursor: pointer;
            }

            .product-image-upload__dropzone:focus {
                outline: 2px solid #6366f1;
                outline-offset: 4px;
            }

            .product-image-upload__dropzone .profile-left {
                width: 100%;
            }

            .product-image-upload__dropzone .profile-image {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 12px;
                margin-bottom: 16px;
            }

            .product-image-upload__dropzone .profile-image img {
                width: 140px;
                height: 140px;
                border-radius: 18px;
                object-fit: cover;
                box-shadow: 0 12px 30px rgba(15, 23, 42, 0.12);
            }

            .product-image-upload__dropzone span {
                display: block;
                color: #64748b;
                font-size: 14px;
            }

        .product-image-upload__input input {
            display: none;
        }

        .product-image-upload__input {
            display: inline-flex;
            align-items: center;
            justify-content: center;
        }

        .product-image-upload__input .rts-btn {
            cursor: pointer;
        }

            .product-image-upload__current {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 12px;
                text-align: center;
                padding: 24px;
                border: 1px solid #e2e8f0;
                border-radius: 18px;
                background: #ffffff;
            }

            .product-image-upload__current img {
                width: 100%;
                border-radius: 16px;
                object-fit: cover;
                box-shadow: 0 16px 40px rgba(15, 23, 42, 0.12);
            }

            .product-image-upload__current-label {
                font-weight: 600;
                color: #0f172a;
            }

            .product-form-page__sidebar {
                display: flex;
                flex-direction: column;
                gap: 24px;
            }

            .product-preview-card {
                background: linear-gradient(135deg, rgba(14, 116, 144, 0.12), rgba(59, 130, 246, 0.08));
                border-radius: 20px;
                padding: 24px;
                display: flex;
                flex-direction: column;
                gap: 16px;
            }

            .product-preview-card__header {
                display: flex;
                align-items: center;
                justify-content: space-between;
            }

            .product-preview-card__header h3 {
                margin: 0;
                font-size: 18px;
                color: #0f172a;
                font-weight: 600;
            }

            .product-preview-card__status {
                display: inline-flex;
                align-items: center;
                gap: 6px;
                padding: 4px 10px;
                border-radius: 999px;
                font-size: 12px;
                font-weight: 600;
                text-transform: capitalize;
            }

            .product-preview-card__status--active {
                background: #dcfce7;
                color: #166534;
            }

            .product-preview-card__status--inactive {
                background: #fee2e2;
                color: #b91c1c;
            }

            .product-preview-card__status--draft {
                background: #fef3c7;
                color: #92400e;
            }

            .product-preview-card__status--out_of_stock {
                background: #ede9fe;
                color: #5b21b6;
            }

            .product-preview-card__body {
                display: grid;
                gap: 16px;
            }

            .product-preview-card__media img {
                width: 100%;
                border-radius: 16px;
                object-fit: cover;
                box-shadow: 0 16px 32px rgba(15, 23, 42, 0.14);
            }

            .product-preview-card__content h4 {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
                color: #0f172a;
            }

            .product-preview-card__content p {
                margin: 8px 0 12px 0;
                color: #334155;
                line-height: 1.6;
            }

            .product-preview-card__pricing {
                display: flex;
                flex-direction: column;
                gap: 6px;
                font-weight: 600;
                color: #0f172a;
            }

            .product-preview-card__sale {
                font-size: 14px;
                color: #dc2626;
            }

            .product-preview-card__meta {
                display: flex;
                flex-direction: column;
                gap: 4px;
                font-size: 13px;
                color: #475569;
            }

            .product-preview-card__meta span {
                display: inline-flex;
                align-items: center;
                gap: 6px;
            }

            .product-preview-card__badge {
                display: inline-flex;
                align-items: center;
                gap: 6px;
                padding: 4px 10px;
                border-radius: 999px;
                background: rgba(252, 211, 77, 0.25);
                color: #92400e;
                font-size: 12px;
                font-weight: 600;
                text-transform: uppercase;
            }

            .product-form-tips {
                background: #ffffff;
                border: 1px solid #e2e8f0;
                border-radius: 18px;
                padding: 20px 22px;
            }

            .product-form-tips h3 {
                margin: 0 0 12px 0;
                font-size: 16px;
                font-weight: 600;
                color: #0f172a;
            }

            .product-form-tips ul {
                margin: 0;
                padding-left: 18px;
                color: #475569;
                line-height: 1.6;
            }

            .product-form-callout {
                background: rgba(59, 130, 246, 0.08);
                border: 1px solid rgba(59, 130, 246, 0.2);
                border-radius: 16px;
                padding: 18px;
                color: #1d4ed8;
                display: flex;
                flex-direction: column;
                gap: 8px;
            }

            .product-form-callout strong {
                font-size: 14px;
            }

            .admin-form-actions {
                display: flex;
                justify-content: flex-end;
                gap: 12px;
                flex-wrap: wrap;
                margin-top: 32px;
            }

            .admin-form-actions__button {
                min-width: 160px;
                justify-content: center;
            }

            .admin-form-actions__button--secondary {
                background: transparent;
                color: #2563eb;
                border-color: #bfdbfe;
            }

            .admin-form-actions__button--secondary:hover,
            .admin-form-actions__button--secondary:focus {
                background: #2563eb;
                color: #ffffff;
                border-color: #2563eb;
            }

            @media (max-width: 1200px) {
                .product-image-upload__wrapper {
                    grid-template-columns: 1fr;
                }
            }

            @media (max-width: 991px) {
                .product-form-page .table-product-select {
                    padding: 24px;
                }

                .product-form-page .right-collups-area-top {
                    flex-direction: column;
                    align-items: stretch;
                }

                .product-form-page__layout {
                    grid-template-columns: 1fr;
                }

                .product-form-page__sidebar {
                    order: -1;
                }
            }

            @media (max-width: 767px) {
                .product-form-section {
                    padding: 20px 22px;
                }

                .admin-form-actions {
                    flex-direction: column;
                    align-items: stretch;
                }

                .admin-form-actions__button {
                    width: 100%;
                    text-align: center;
                }
            }
        </style>
    </head>
<body>
    <div class="rcf_dashboard">
        <?php render_admin_sidebar($dashboardData['sidebar']); ?>
        <div class="right-area-body-content">
            <?php render_admin_header($dashboardData['header']); ?>
            <div class="body-root-inner">
                <div class="transection product-form-page">
                    <div class="vendor-list-main-wrapper product-wrapper add-product-page">
                        <div class="card-body table-product-select">
                            <div class="header-two show right-collups-add-product">
                                <div class="right-collups-area-top">
                                    <div>
                                        <div class="product-form-page__subtitle"><?= $formMode === 'edit' ? 'Update product' : 'Create product'; ?></div>
                                        <h1 class="title"><?= htmlspecialchars($pageTitle, ENT_QUOTES, 'UTF-8'); ?></h1>
                                        <p><?= htmlspecialchars($formDescription, ENT_QUOTES, 'UTF-8'); ?></p>
                                    </div>
                                    <a href="product-list.php" class="rts-btn btn-primary bg-transparent product-form-page__back">
                                        <span aria-hidden="true">⟵</span>
                                        <span>Back to Products</span>
                                    </a>
                                </div>

                                <?php if (!empty($flashMessages)): ?>
                                    <div class="product-form-page__alerts">
                                        <?php foreach ($flashMessages as $type => $messages): ?>
                                            <?php foreach ($messages as $message): ?>
                                                <div class="product-form-page__alert product-form-page__alert--<?= $type === 'success' ? 'success' : 'error'; ?>">
                                                    <?= htmlspecialchars($message, ENT_QUOTES, 'UTF-8'); ?>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php endforeach; ?>
                                    </div>
                                <?php endif; ?>

                                <?php if (!empty($errors['general'])): ?>
                                    <div class="product-form-page__alert product-form-page__alert--error">
                                        <?= htmlspecialchars($errors['general'], ENT_QUOTES, 'UTF-8'); ?>
                                    </div>
                                <?php endif; ?>

                                <form method="post" enctype="multipart/form-data" class="product-form" id="product-form" data-form-mode="<?= htmlspecialchars($productFormMode, ENT_QUOTES, 'UTF-8'); ?>">
                                    <input type="hidden" name="form_action" value="<?= $formMode === 'edit' ? 'update' : 'create'; ?>">
                                    <?php if ($formMode === 'edit' && $editingId !== null): ?>
                                        <input type="hidden" name="product_id" value="<?= htmlspecialchars($editingId, ENT_QUOTES, 'UTF-8'); ?>">
                                    <?php endif; ?>
                                    <input type="hidden" name="existing_image_path" value="<?= htmlspecialchars($formData['image_path'], ENT_QUOTES, 'UTF-8'); ?>">

                                    <div class="product-form-page__layout">
                                        <div class="product-form-page__main">
                                            <section class="product-form-section">
                                                <header class="product-form-section__header">
                                                    <span class="product-form-section__kicker">Product details</span>
                                                    <h2 class="product-form-section__title">General Information</h2>
                                                    <p class="product-form-section__description">Give shoppers a clear and compelling summary of the product. A strong title and description help boost conversions.</p>
                                                </header>
                                                <div class="product-form-section__grid product-form-section__grid--single">
                                                    <div class="single-input">
                                                        <label for="product-name">Product Name *</label>
                                                        <input type="text" id="product-name" name="name" maxlength="255" value="<?= htmlspecialchars($formData['name'], ENT_QUOTES, 'UTF-8'); ?>" placeholder="e.g. Handcrafted Oak Coffee Table" required>
                                                        <?php if (!empty($errors['name'])): ?>
                                                            <small class="field-error">
                                                                <?= htmlspecialchars($errors['name'], ENT_QUOTES, 'UTF-8'); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="single-input">
                                                        <label for="product-description">Description</label>
                                                        <textarea id="product-description" name="description" maxlength="5000" placeholder="Share key details, materials, features, and care instructions."><?= htmlspecialchars($formData['description'], ENT_QUOTES, 'UTF-8'); ?></textarea>
                                                        <?php if (!empty($errors['description'])): ?>
                                                            <small class="field-error">
                                                                <?= htmlspecialchars($errors['description'], ENT_QUOTES, 'UTF-8'); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <div class="product-form-section__grid">
                                                    <div class="single-input">
                                                        <label for="product-category">Category</label>
                                                        <select id="product-category" name="category_id" class="mySelect">
                                                            <option value="" data-display="Select Category">Select Category</option>
                                                            <?php foreach ($categories as $category): ?>
                                                                <option value="<?= htmlspecialchars($category['id'], ENT_QUOTES, 'UTF-8'); ?>" data-display="<?= htmlspecialchars($category['name'], ENT_QUOTES, 'UTF-8'); ?>"<?= $formData['category_id'] === $category['id'] ? ' selected' : ''; ?>><?= htmlspecialchars($category['name'], ENT_QUOTES, 'UTF-8'); ?></option>
                                                            <?php endforeach; ?>
                                                        </select>
                                                        <?php if (!empty($errors['category_id'])): ?>
                                                            <small class="field-error">
                                                                <?= htmlspecialchars($errors['category_id'], ENT_QUOTES, 'UTF-8'); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="single-input">
                                                        <label for="product-tags">Tags</label>
                                                        <input type="text" id="product-tags" name="tags" value="<?= htmlspecialchars($formData['tags'], ENT_QUOTES, 'UTF-8'); ?>" placeholder="e.g. handmade, sustainable, living room">
                                                        <small class="product-form-page__helper">Separate tags with commas to aid internal search.</small>
                                                        <?php if (!empty($errors['tags'])): ?>
                                                            <small class="field-error">
                                                                <?= htmlspecialchars($errors['tags'], ENT_QUOTES, 'UTF-8'); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </section>

                                            <section class="product-form-section">
                                                <header class="product-form-section__header">
                                                    <span class="product-form-section__kicker">Pricing &amp; availability</span>
                                                    <h2 class="product-form-section__title">Pricing and Inventory</h2>
                                                    <p class="product-form-section__description">Set accurate pricing and stock levels to ensure storefront information is reliable.</p>
                                                </header>
                                                <div class="product-form-section__grid">
                                                    <div class="single-input">
                                                        <label for="product-price">Regular Price *</label>
                                                        <input type="number" id="product-price" name="price" step="0.01" min="0" value="<?= htmlspecialchars($formData['price'], ENT_QUOTES, 'UTF-8'); ?>" placeholder="e.g. 120.00" required>
                                                        <?php if (!empty($errors['price'])): ?>
                                                            <small class="field-error">
                                                                <?= htmlspecialchars($errors['price'], ENT_QUOTES, 'UTF-8'); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="single-input">
                                                        <label for="product-sale-price">Sale Price</label>
                                                        <input type="number" id="product-sale-price" name="sale_price" step="0.01" min="0" value="<?= htmlspecialchars($formData['sale_price'], ENT_QUOTES, 'UTF-8'); ?>" placeholder="Leave blank if not discounted">
                                                        <?php if (!empty($errors['sale_price'])): ?>
                                                            <small class="field-error">
                                                                <?= htmlspecialchars($errors['sale_price'], ENT_QUOTES, 'UTF-8'); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="single-input">
                                                        <label for="product-stock-quantity">Stock Quantity</label>
                                                        <input type="number" id="product-stock-quantity" name="stock_quantity" min="0" value="<?= htmlspecialchars($formData['stock_quantity'], ENT_QUOTES, 'UTF-8'); ?>" placeholder="e.g. 25">
                                                        <?php if (!empty($errors['stock_quantity'])): ?>
                                                            <small class="field-error">
                                                                <?= htmlspecialchars($errors['stock_quantity'], ENT_QUOTES, 'UTF-8'); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="single-input">
                                                        <label for="product-status">Status</label>
                                                        <select id="product-status" name="status" class="mySelect">
                                                            <?php foreach ($statusOptions as $value => $label): ?>
                                                                <option value="<?= htmlspecialchars($value, ENT_QUOTES, 'UTF-8'); ?>" data-display="<?= htmlspecialchars($label, ENT_QUOTES, 'UTF-8'); ?>"<?= $formData['status'] === $value ? ' selected' : ''; ?>><?= htmlspecialchars($label, ENT_QUOTES, 'UTF-8'); ?></option>
                                                            <?php endforeach; ?>
                                                        </select>
                                                        <?php if (!empty($errors['status'])): ?>
                                                            <small class="field-error">
                                                                <?= htmlspecialchars($errors['status'], ENT_QUOTES, 'UTF-8'); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                                <div class="product-form-section__grid">
                                                    <div class="single-input">
                                                        <label for="product-sku">SKU</label>
                                                        <input type="text" id="product-sku" name="sku" value="<?= htmlspecialchars($formData['sku'], ENT_QUOTES, 'UTF-8'); ?>" placeholder="e.g. SKU-00123">
                                                        <?php if (!empty($errors['sku'])): ?>
                                                            <small class="field-error">
                                                                <?= htmlspecialchars($errors['sku'], ENT_QUOTES, 'UTF-8'); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="single-input" style="align-self: end;">
                                                        <label class="product-form-page__helper" for="product-featured" style="font-weight: 600; color: #0f172a;">Featured Product</label>
                                                        <label style="display:flex; align-items:center; gap:8px;">
                                                            <input type="checkbox" id="product-featured" name="featured" value="1" <?= $formData['featured'] ? 'checked' : ''; ?>>
                                                            Highlight this product on curated sections
                                                        </label>
                                                    </div>
                                                </div>
                                                <div class="product-form-callout" role="note">
                                                    <strong>Tip:</strong>
                                                    <span>Set the status to Draft while building out product details. Switch to Active once everything looks correct.</span>
                                                </div>
                                            </section>

                                            <section class="product-form-section">
                                                <header class="product-form-section__header">
                                                    <span class="product-form-section__kicker">Shipping &amp; sizing</span>
                                                    <h2 class="product-form-section__title">Dimensions</h2>
                                                    <p class="product-form-section__description">Dimensions help customers understand scale and enable more accurate shipping estimates.</p>
                                                </header>
                                                <div class="product-form-section__grid">
                                                    <div class="single-input">
                                                        <label for="product-weight">Weight (kg)</label>
                                                        <input type="number" id="product-weight" name="weight" step="0.01" min="0" value="<?= htmlspecialchars($formData['weight'], ENT_QUOTES, 'UTF-8'); ?>" placeholder="e.g. 1.25">
                                                        <?php if (!empty($errors['weight'])): ?>
                                                            <small class="field-error">
                                                                <?= htmlspecialchars($errors['weight'], ENT_QUOTES, 'UTF-8'); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="single-input">
                                                        <label for="product-length">Length (cm)</label>
                                                        <input type="number" id="product-length" name="length" step="0.01" min="0" value="<?= htmlspecialchars($formData['length'], ENT_QUOTES, 'UTF-8'); ?>" placeholder="e.g. 45">
                                                        <?php if (!empty($errors['length'])): ?>
                                                            <small class="field-error">
                                                                <?= htmlspecialchars($errors['length'], ENT_QUOTES, 'UTF-8'); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="single-input">
                                                        <label for="product-width">Width (cm)</label>
                                                        <input type="number" id="product-width" name="width" step="0.01" min="0" value="<?= htmlspecialchars($formData['width'], ENT_QUOTES, 'UTF-8'); ?>" placeholder="e.g. 30">
                                                        <?php if (!empty($errors['width'])): ?>
                                                            <small class="field-error">
                                                                <?= htmlspecialchars($errors['width'], ENT_QUOTES, 'UTF-8'); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                    <div class="single-input">
                                                        <label for="product-height">Height (cm)</label>
                                                        <input type="number" id="product-height" name="height" step="0.01" min="0" value="<?= htmlspecialchars($formData['height'], ENT_QUOTES, 'UTF-8'); ?>" placeholder="e.g. 12">
                                                        <?php if (!empty($errors['height'])): ?>
                                                            <small class="field-error">
                                                                <?= htmlspecialchars($errors['height'], ENT_QUOTES, 'UTF-8'); ?>
                                                            </small>
                                                        <?php endif; ?>
                                                    </div>
                                                </div>
                                            </section>

                                            <section class="product-form-section">
                                                <header class="product-form-section__header">
                                                    <span class="product-form-section__kicker">Media</span>
                                                    <h2 class="product-form-section__title">Product Imagery</h2>
                                                    <p class="product-form-section__description">Upload a crisp, high-quality image to showcase your product in listings and detail pages.</p>
                                                </header>
                                                <div class="single-input product-image-upload">
                                                    <div class="product-image-upload__wrapper">
                                                        <div class="product-image-upload__dropzone" role="button" tabindex="0" aria-describedby="product-image-guidance">
                                                            <div class="profile-left">
                                                                <div class="profile-image">
                                                                    <img id="product-image-preview" src="<?= htmlspecialchars($currentProductImage, ENT_QUOTES, 'UTF-8'); ?>" data-original-src="<?= htmlspecialchars($currentProductImage, ENT_QUOTES, 'UTF-8'); ?>" alt="Product image preview">
                                                                    <span><?= htmlspecialchars($imageUploadMessage, ENT_QUOTES, 'UTF-8'); ?></span>
                                                                </div>
                                                                <div class="button-area">
                                                                    <label class="brows-file-wrapper product-image-upload__input">
                                                                        <input type="file" id="product-image" name="product_image" accept="image/png,image/jpeg,image/webp" autocomplete="off">
                                                                        <span class="rts-btn btn-primary">Browse Files</span>
                                                                    </label>
                                                                    <span class="product-form-page__helper" id="product-image-guidance">Upload a JPG, PNG, or WEBP up to 5MB. Square images (512×512) work best.</span>
                                                                </div>
                                                            </div>
                                                        </div>
                                                        <?php if (!empty($formData['image_path'])):
                                                            $resolvedExistingImage = resolve_product_image_url($formData['image_path']);
                                                            if ($resolvedExistingImage !== null): ?>
                                                            <div class="product-image-upload__current">
                                                                <span class="product-image-upload__current-label">Current image</span>
                                                                <img src="<?= htmlspecialchars($resolvedExistingImage, ENT_QUOTES, 'UTF-8'); ?>" alt="Current product image">
                                                                <span class="product-form-page__helper">Leave the field empty to keep this image.</span>
                                                            </div>
                                                        <?php endif; endif; ?>
                                                    </div>
                                                    <?php if (!empty($errors['image'])): ?>
                                                        <small class="field-error">
                                                            <?= htmlspecialchars($errors['image'], ENT_QUOTES, 'UTF-8'); ?>
                                                        </small>
                                                    <?php endif; ?>
                                                </div>
                                            </section>
                                        </div>
                                        <aside class="product-form-page__sidebar" id="product-sidebar">
                                            <div class="product-preview-card">
                                                <div class="product-preview-card__header">
                                                    <h3>Live Preview</h3>
                                                    <span class="product-preview-card__status product-preview-card__status--<?= htmlspecialchars($statusModifier, ENT_QUOTES, 'UTF-8'); ?>" id="product-preview-status" data-status="<?= htmlspecialchars($formData['status'], ENT_QUOTES, 'UTF-8'); ?>">
                                                        <?= htmlspecialchars($previewStatusLabel, ENT_QUOTES, 'UTF-8'); ?>
                                                    </span>
                                                </div>
                                                <div class="product-preview-card__body">
                                                    <div class="product-preview-card__media">
                                                        <img id="product-preview-image" src="<?= htmlspecialchars($currentProductImage, ENT_QUOTES, 'UTF-8'); ?>" data-original-src="<?= htmlspecialchars($currentProductImage, ENT_QUOTES, 'UTF-8'); ?>" alt="Product artwork preview">
                                                    </div>
                                                    <div class="product-preview-card__content">
                                                        <h4 id="product-preview-name" data-default-value="<?= htmlspecialchars($defaultPreviewName, ENT_QUOTES, 'UTF-8'); ?>"><?= htmlspecialchars($previewName, ENT_QUOTES, 'UTF-8'); ?></h4>
                                                        <p id="product-preview-description" data-default-value="<?= htmlspecialchars($defaultPreviewDescription, ENT_QUOTES, 'UTF-8'); ?>"><?= htmlspecialchars($previewDescription, ENT_QUOTES, 'UTF-8'); ?></p>
                                                        <div class="product-preview-card__pricing">
                                                            <strong id="product-preview-price"><?= htmlspecialchars($previewPriceLabel, ENT_QUOTES, 'UTF-8'); ?></strong>
                                                            <span id="product-preview-sale-price" class="product-preview-card__sale"<?= $previewSalePriceLabel === null ? ' style="display:none;"' : ''; ?>><?= $previewSalePriceLabel !== null ? 'Sale: ' . htmlspecialchars($previewSalePriceLabel, ENT_QUOTES, 'UTF-8') : ''; ?></span>
                                                        </div>
                                                        <div class="product-preview-card__meta">
                                                            <span id="product-preview-stock" data-default-value="0">Stock: <?= number_format($previewStockLabel); ?></span>
                                                            <span id="product-preview-category" data-default-value="<?= htmlspecialchars($defaultPreviewCategory, ENT_QUOTES, 'UTF-8'); ?>">Category: <?= htmlspecialchars($previewCategory, ENT_QUOTES, 'UTF-8'); ?></span>
                                                            <span id="product-preview-featured" class="product-preview-card__badge"<?= $formData['featured'] ? '' : ' style="display:none;"'; ?>>Featured</span>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="product-form-tips">
                                                <h3>Best Practices</h3>
                                                <ul>
                                                    <li>Use descriptive titles that highlight materials, size, or unique qualities.</li>
                                                    <li>Keep pricing consistent with storefront promotions to avoid surprises.</li>
                                                    <li>Refresh imagery periodically to keep the catalogue feeling current.</li>
                                                </ul>
                                            </div>
                                        </aside>
                                    </div>

                                    <div class="admin-form-actions">
                                        <button type="submit" class="rts-btn btn-primary admin-form-actions__button" data-loading-text="Saving...">Save Product</button>
                                        <?php if ($formMode === 'edit'): ?>
                                            <a class="rts-btn btn-primary admin-form-actions__button admin-form-actions__button--secondary" href="product-list.php">Cancel</a>
                                        <?php else: ?>
                                            <button type="reset" class="rts-btn btn-primary admin-form-actions__button admin-form-actions__button--secondary">Reset</button>
                                        <?php endif; ?>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>
                </div>
                <?php render_admin_footer($dashboardData['footer']); ?>
            </div>
        </div>
    </div>
    <?php render_admin_overlays(); ?>
    <script src="assets/js/plugins.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/apexcharts@3.27.0/dist/apexcharts.min.js"></script>
    <script src="https://cdn.datatables.net/1.10.15/js/jquery.dataTables.min.js"></script>
    <script src="assets/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            var form = document.getElementById('product-form');
            if (!form) {
                return;
            }
            var formMode = form ? form.getAttribute('data-form-mode') : 'create';
            var nameInput = document.getElementById('product-name');
            var descriptionInput = document.getElementById('product-description');
            var priceInput = document.getElementById('product-price');
            var salePriceInput = document.getElementById('product-sale-price');
            var stockInput = document.getElementById('product-stock-quantity');
            var statusSelect = document.getElementById('product-status');
            var categorySelect = document.getElementById('product-category');
            var featuredCheckbox = document.getElementById('product-featured');
            var imageDropzone = document.querySelector('.product-image-upload__dropzone');
            var imageInput = form.querySelector('#product-image');
            var imageBrowse = form.querySelector('.product-image-upload__input');


            var imagePreview = document.getElementById('product-image-preview');
            var previewImage = document.getElementById('product-preview-image');
            var previewName = document.getElementById('product-preview-name');
            var previewDescription = document.getElementById('product-preview-description');
            var previewPrice = document.getElementById('product-preview-price');
            var previewSalePrice = document.getElementById('product-preview-sale-price');
            var previewStock = document.getElementById('product-preview-stock');
            var previewCategory = document.getElementById('product-preview-category');
            var previewStatus = document.getElementById('product-preview-status');
            var previewFeatured = document.getElementById('product-preview-featured');
            var submitButton = document.querySelector('[data-loading-text]');
            var NAME_MAX_LENGTH = 255;
            var DESCRIPTION_MAX_LENGTH = 5000;
            var statusLabels = <?= json_encode($statusOptions, JSON_HEX_TAG | JSON_HEX_APOS | JSON_HEX_QUOT | JSON_HEX_AMP); ?>;
            var defaultName = previewName ? previewName.dataset.defaultValue : 'New product';
            var defaultDescription = previewDescription ? previewDescription.dataset.defaultValue : '';
            var defaultCategory = previewCategory ? previewCategory.dataset.defaultValue : 'Unassigned';
            var originalFormImage = imagePreview ? imagePreview.getAttribute('data-original-src') : null;
            var originalPreviewImage = previewImage ? previewImage.getAttribute('data-original-src') : null;

            function formatCurrency(value) {
                if (isNaN(value) || value === null) {
                    return '$0.00';
                }
                return '$' + Number(value).toFixed(2);
            }

            function openImagePicker() {
                if (imageInput) {
                    imageInput.click();
                }
            }

            function updatePreviewName(value) {
                if (!previewName) {
                    return;
                }
                var displayValue = value && value.trim() !== '' ? value.trim() : defaultName;
                previewName.textContent = displayValue;
            }

            function updatePreviewDescription(value) {
                if (!previewDescription) {
                    return;
                }
                var displayValue = value && value.trim() !== '' ? value.trim() : defaultDescription;
                previewDescription.textContent = displayValue;
            }

            function updatePreviewPrice() {
                if (!previewPrice) {
                    return;
                }
                var priceValue = parseFloat(priceInput && priceInput.value ? priceInput.value : '0');
                var saleValue = parseFloat(salePriceInput && salePriceInput.value ? salePriceInput.value : '0');
                var hasPrice = !isNaN(priceValue) && priceValue >= 0;
                var hasSale = !isNaN(saleValue) && saleValue > 0 && (isNaN(priceValue) || saleValue < priceValue);

                previewPrice.textContent = formatCurrency(hasPrice ? priceValue : 0);

                if (previewSalePrice) {
                    if (hasSale) {
                        previewSalePrice.textContent = 'Sale: ' + formatCurrency(saleValue);
                        previewSalePrice.style.display = '';
                    } else {
                        previewSalePrice.textContent = '';
                        previewSalePrice.style.display = 'none';
                    }
                }
            }

            function updatePreviewStock(value) {
                if (!previewStock) {
                    return;
                }
                var numericValue = parseInt(value, 10);
                if (isNaN(numericValue) || numericValue < 0) {
                    numericValue = 0;
                }
                previewStock.textContent = 'Stock: ' + numericValue;
            }

            function updatePreviewCategory() {
                if (!previewCategory) {
                    return;
                }
                if (!categorySelect || categorySelect.selectedIndex === -1) {
                    previewCategory.textContent = 'Category: ' + defaultCategory;
                    return;
                }
                var selected = categorySelect.options[categorySelect.selectedIndex];
                var label = selected && selected.value !== '' ? selected.textContent.trim() : defaultCategory;
                previewCategory.textContent = 'Category: ' + label;
            }

            function updateStatusBadge(value) {
                if (!previewStatus) {
                    return;
                }
                var label = statusLabels[value] || value;
                previewStatus.textContent = label;
                previewStatus.dataset.status = value;
                previewStatus.classList.remove(
                    'product-preview-card__status--active',
                    'product-preview-card__status--inactive',
                    'product-preview-card__status--draft',
                    'product-preview-card__status--out_of_stock'
                );
                previewStatus.classList.add('product-preview-card__status--' + value);
            }

            function updateFeaturedBadge() {
                if (!previewFeatured) {
                    return;
                }
                if (featuredCheckbox && featuredCheckbox.checked) {
                    previewFeatured.style.display = '';
                } else {
                    previewFeatured.style.display = 'none';
                }
            }

            function clearFieldError(field) {
                if (!field) {
                    return;
                }
                field.classList.remove('error');
                var parent = field.parentNode;
                if (!parent) {
                    return;
                }
                var existingError = parent.querySelector('.field-error');
                if (existingError) {
                    if (!existingError.classList.contains('persistent-error')) {
                        existingError.remove();
                    }
                }
            }

            function setFieldError(field, message) {
                if (!field) {
                    return;
                }
                clearFieldError(field);
                field.classList.add('error');
                var parent = field.parentNode;
                if (!parent) {
                    return;
                }
                var errorElement = document.createElement('small');
                errorElement.className = 'field-error';
                errorElement.textContent = message;
                parent.appendChild(errorElement);
            }

            function validateName() {
                if (!nameInput) {
                    return true;
                }
                var value = nameInput.value.trim();
                clearFieldError(nameInput);
                if (value === '') {
                    setFieldError(nameInput, 'Product name is required.');
                    return false;
                }
                if (value.length > NAME_MAX_LENGTH) {
                    setFieldError(nameInput, 'Product name must be ' + NAME_MAX_LENGTH + ' characters or fewer.');
                    return false;
                }
                return true;
            }

            function validatePrice() {
                if (!priceInput) {
                    return true;
                }
                var value = priceInput.value.trim();
                clearFieldError(priceInput);
                if (value === '') {
                    if (formMode === 'create') {
                        setFieldError(priceInput, 'Price is required for new products.');
                        return false;
                    }
                    return true;
                }
                var numericValue = Number(value);
                if (!isFinite(numericValue) || numericValue < 0) {
                    setFieldError(priceInput, 'Please enter a valid price (0 or greater).');
                    return false;
                }
                return true;
            }

            function validateSalePrice() {
                if (!salePriceInput) {
                    return true;
                }
                var saleValue = salePriceInput.value.trim();
                clearFieldError(salePriceInput);
                if (saleValue === '') {
                    return true;
                }
                var numericSale = Number(saleValue);
                if (!isFinite(numericSale) || numericSale < 0) {
                    setFieldError(salePriceInput, 'Please enter a valid sale price (0 or greater).');
                    return false;
                }
                var priceValue = priceInput && priceInput.value !== '' ? Number(priceInput.value) : null;
                if (priceValue !== null && isFinite(priceValue) && numericSale >= priceValue) {
                    setFieldError(salePriceInput, 'Sale price must be less than the regular price.');
                    return false;
                }
                return true;
            }

            function validateStock() {
                if (!stockInput) {
                    return true;
                }
                var stockValue = stockInput.value.trim();
                clearFieldError(stockInput);
                if (stockValue === '') {
                    return true;
                }
                var numericStock = Number(stockValue);
                if (!Number.isInteger(numericStock) || numericStock < 0) {
                    setFieldError(stockInput, 'Please enter a valid stock quantity (0 or greater).');
                    return false;
                }
                return true;
            }

            function addCharacterCounter(field, maxLength) {
                if (!field || field.parentNode.querySelector('.character-counter')) {
                    return;
                }
                var counter = document.createElement('small');
                counter.className = 'character-counter';
                field.parentNode.appendChild(counter);

                function updateCounter() {
                    var remaining = maxLength - field.value.length;
                    counter.textContent = remaining + ' characters remaining';
                    counter.style.color = remaining < 20 ? '#d32f2f' : '#64748b';
                }

                updateCounter();
                field.addEventListener('input', updateCounter);
            }

            if (imageDropzone) {
                imageDropzone.addEventListener('click', function (event) {
                    if (event.target && event.target.closest('.product-image-upload__input')) {
                        return; // allow native label behaviour
                    }
                    event.preventDefault();
                    openImagePicker();
                });
                imageDropzone.addEventListener('keydown', function (event) {
                    if (event.key === 'Enter' || event.key === ' ') {
                        event.preventDefault();
                        openImagePicker();
                    }
                });
            }

            if (imageInput) {
                imageInput.addEventListener('change', function (event) {
                    var file = event.target.files && event.target.files[0];
                    if (file) {
                        var objectUrl = URL.createObjectURL(file);
                        if (imagePreview) {
                            imagePreview.src = objectUrl;
                            imagePreview.onload = function () {
                                URL.revokeObjectURL(objectUrl);
                            };
                        }
                        if (previewImage) {
                            previewImage.src = objectUrl;
                        }
                    } else {
                        if (imagePreview && originalFormImage) {
                            imagePreview.src = originalFormImage;
                        }
                        if (previewImage && originalPreviewImage) {
                            previewImage.src = originalPreviewImage;
                        }
                    }
                });
            }

            if (nameInput) {
                addCharacterCounter(nameInput, NAME_MAX_LENGTH);
                nameInput.addEventListener('blur', validateName);
                nameInput.addEventListener('input', function () {
                    if (nameInput.value.trim() !== '') {
                        validateName();
                    }
                    updatePreviewName(nameInput.value);
                });
                updatePreviewName(nameInput.value);
            }

            if (descriptionInput) {
                addCharacterCounter(descriptionInput, DESCRIPTION_MAX_LENGTH);
                descriptionInput.addEventListener('blur', function () {
                    if (descriptionInput.value.length > DESCRIPTION_MAX_LENGTH) {
                        setFieldError(descriptionInput, 'Description must be ' + DESCRIPTION_MAX_LENGTH + ' characters or fewer.');
                    }
                });
                descriptionInput.addEventListener('input', function () {
                    clearFieldError(descriptionInput);
                    if (descriptionInput.value.length > DESCRIPTION_MAX_LENGTH) {
                        setFieldError(descriptionInput, 'Description must be ' + DESCRIPTION_MAX_LENGTH + ' characters or fewer.');
                    }
                    updatePreviewDescription(descriptionInput.value);
                });
                updatePreviewDescription(descriptionInput.value);
            }

            if (priceInput) {
                priceInput.addEventListener('blur', validatePrice);
                priceInput.addEventListener('input', function () {
                    clearFieldError(priceInput);
                    updatePreviewPrice();
                });
                updatePreviewPrice();
            }

            if (salePriceInput) {
                salePriceInput.addEventListener('blur', validateSalePrice);
                salePriceInput.addEventListener('input', function () {
                    clearFieldError(salePriceInput);
                    updatePreviewPrice();
                });
                updatePreviewPrice();
            }

            if (stockInput) {
                stockInput.addEventListener('blur', validateStock);
                stockInput.addEventListener('input', function () {
                    clearFieldError(stockInput);
                    updatePreviewStock(stockInput.value);
                });
                updatePreviewStock(stockInput.value);
            }

            if (statusSelect) {
                statusSelect.addEventListener('change', function () {
                    updateStatusBadge(statusSelect.value);
                });
                updateStatusBadge(statusSelect.value);
            }

            if (categorySelect) {
                categorySelect.addEventListener('change', updatePreviewCategory);
                updatePreviewCategory();
            }

            if (featuredCheckbox) {
                featuredCheckbox.addEventListener('change', updateFeaturedBadge);
                updateFeaturedBadge();
            }

            if (form) {
                form.addEventListener('submit', function (event) {
                    var isFormValid = true;

                    if (!validateName()) {
                        isFormValid = false;
                    }
                    if (!validatePrice()) {
                        isFormValid = false;
                    }
                    if (!validateSalePrice()) {
                        isFormValid = false;
                    }
                    if (!validateStock()) {
                        isFormValid = false;
                    }

                    if (!isFormValid) {
                        event.preventDefault();
                        return false;
                    }

                    if (submitButton && !submitButton.dataset.submitting) {
                        submitButton.dataset.submitting = 'true';
                        submitButton.classList.add('disabled');
                        submitButton.disabled = true;
                        var loadingText = submitButton.getAttribute('data-loading-text') || 'Saving...';
                        submitButton.dataset.originalLabel = submitButton.innerHTML;
                        submitButton.innerHTML = loadingText;
                    }

                    return true;
                });

                form.addEventListener('reset', function () {
                    window.setTimeout(function () {
                        if (nameInput) {
                            updatePreviewName(nameInput.value);
                        }
                        if (descriptionInput) {
                            updatePreviewDescription(descriptionInput.value);
                        }
                        if (priceInput || salePriceInput) {
                            updatePreviewPrice();
                        }
                        if (stockInput) {
                            updatePreviewStock(stockInput.value);
                        }
                        if (statusSelect) {
                            updateStatusBadge(statusSelect.value);
                        }
                        if (categorySelect) {
                            updatePreviewCategory();
                        }
                        if (featuredCheckbox) {
                            updateFeaturedBadge();
                        }
                        if (imagePreview && originalFormImage) {
                            imagePreview.src = originalFormImage;
                        }
                        if (previewImage && originalPreviewImage) {
                            previewImage.src = originalPreviewImage;
                        }
                    }, 0);
                });
            }
        });
    </script>
</body>
</html>

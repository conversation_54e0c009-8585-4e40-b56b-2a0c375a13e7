<?php
require_once __DIR__ . '/bootstrap.php';

$pageTitle = 'Shop Grid Sidebar';

$categorySlug = trim((string) ($_GET['category'] ?? ''));
$searchQuery = trim((string) ($_GET['q'] ?? ''));
$minPrice = trim((string) ($_GET['min_price'] ?? ''));
$maxPrice = trim((string) ($_GET['max_price'] ?? ''));
$sortOption = trim((string) ($_GET['sort'] ?? ''));
$page = max(1, (int) ($_GET['page'] ?? 1));
$perPage = 12;

$filters = ['status' => 'active'];

if ($searchQuery !== '') {
    $filters['search'] = $searchQuery;
}

if ($minPrice !== '' && is_numeric($minPrice)) {
    $filters['min_price'] = (float) $minPrice;
}

if ($maxPrice !== '' && is_numeric($maxPrice)) {
    $filters['max_price'] = (float) $maxPrice;
}

if ($sortOption !== '') {
    $filters['sort'] = $sortOption;
}

$categoryDoc = null;
$categoryWarning = null;
if ($categorySlug !== '') {
    $categoryDoc = get_category_by_slug($categorySlug);
    if ($categoryDoc) {
        $filters['category_id'] = $categoryDoc['id'] ?? null;
        $filters['category_slug'] = $categoryDoc['slug'] ?? null;
    } else {
        $categoryWarning = 'The selected category could not be found.';
    }
}

$productsResult = get_products_with_filters($filters, $page, $perPage);
$productItems = $productsResult['items'] ?? [];
$pagination = $productsResult['pagination'] ?? [
    'current_page' => $page,
    'per_page' => $perPage,
    'total' => count($productItems),
    'total_pages' => max(1, (int) ceil(max(1, count($productItems)) / max(1, $perPage))),
    'has_next' => false,
    'has_prev' => $page > 1,
];

$defaultProductActions = [
    [
        'class' => 'single-action openuptip message-show-action',
        'title' => 'Add To Wishlist',
        'icon' => 'fa-light fa-heart',
        'flow' => 'up',
    ],
    [
        'class' => 'single-action openuptip',
        'title' => 'Compare',
        'icon' => 'fa-solid fa-arrows-retweet',
        'flow' => 'up',
        'attributes' => [
            'data-bs-toggle' => 'modal',
            'data-bs-target' => '#exampleModal',
        ],
    ],
    [
        'class' => 'single-action openuptip cta-quickview product-details-popup-btn',
        'title' => 'Quick View',
        'icon' => 'fa-regular fa-eye',
        'flow' => 'up',
    ],
];

$productCards = array_map(function (array $product) use ($defaultProductActions) {
    $card = build_product_card_payload($product, [
        'actions' => $defaultProductActions,
        'class' => 'single-shopping-card-one',
    ]);

    $card['details_button'] = [
        'button' => [
            'class' => 'rts-btn btn-primary radious-sm with-icon',
            'text' => 'View Details',
            'href' => $card['href'] ?? '#',
            'icons' => [
                ['class' => 'fa-regular fa-paper-plane'],
                ['class' => 'fa-regular fa-paper-plane'],
            ],
        ],
    ];

    return $card;
}, $productItems);

$totalProducts = $pagination['total'] ?? count($productItems);
$startIndex = $totalProducts > 0 ? (($pagination['current_page'] - 1) * $pagination['per_page']) + 1 : 0;
$endIndex = $totalProducts > 0 ? min($totalProducts, $startIndex + $pagination['per_page'] - 1) : 0;

$categories = get_categories('active');
if (empty($categories)) {
    $categories = load_sample_categories();
}

$baseQuery = array_filter([
    'category' => $categorySlug !== '' ? $categorySlug : null,
    'q' => $searchQuery !== '' ? $searchQuery : null,
    'min_price' => $minPrice !== '' ? $minPrice : null,
    'max_price' => $maxPrice !== '' ? $maxPrice : null,
    'sort' => $sortOption !== '' ? $sortOption : null,
], fn($value) => $value !== null && $value !== '');

if (!function_exists('build_shop_query_parameters')) {
    function build_shop_query_parameters(array $base, array $overrides = []): string
    {
        $params = array_filter(array_merge($base, $overrides), function ($value) {
            return $value !== null && $value !== '' && $value !== false;
        });

        return empty($params) ? '' : '?' . http_build_query($params);
    }
}

$sortOptions = [
    '' => 'Sort by default',
    'price_asc' => 'Price: Low to High',
    'price_desc' => 'Price: High to Low',
    'name_asc' => 'Name: A to Z',
    'name_desc' => 'Name: Z to A',
    'sales' => 'Best Sellers',
    'popularity' => 'Most Viewed',
];

include 'components/layout/page-start.php';
?>

    <!-- rts navigation bar area start -->
    <div class="rts-navigation-area-breadcrumb">
        <div class="container">
            <div class="row">
                <div class="col-lg-12">
                    <div class="navigator-breadcrumb-wrapper">
                        <a href="index.php">Home</a>
                        <i class="fa-regular fa-chevron-right"></i>
                        <a class="current" href="shop-grid-sidebar.php">Shop Grid Sidebar</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- rts navigation bar area end -->

    <div class="section-seperator">
        <div class="container">
            <hr class="section-seperator">
        </div>
    </div>

    <div class="shop-grid-sidebar-area rts-section-gap">
        <div class="container">
            <div class="row g-0">
                <div class="col-xl-3 col-lg-12 pr--70 pr_lg--10 pr_sm--10 pr_md--5 rts-sticky-column-item">
                    <div class="sidebar-filter-main theiaStickySidebar">
                        <?php if ($categoryWarning !== null): ?>
                            <div class="no-data-message text-center py-3">
                                <p class="mb-0"><?= htmlspecialchars($categoryWarning, ENT_QUOTES, 'UTF-8'); ?></p>
                            </div>
                        <?php endif; ?>
                        <form class="filter-form" method="get" action="">
                            <input type="hidden" name="q" value="<?= htmlspecialchars($searchQuery, ENT_QUOTES, 'UTF-8'); ?>">
                            <input type="hidden" name="sort" value="<?= htmlspecialchars($sortOption, ENT_QUOTES, 'UTF-8'); ?>">
                            <input type="hidden" name="page" value="1">
                            <div class="single-filter-box">
                                <h5 class="title">Price Range</h5>
                                <div class="filterbox-body">
                                    <div class="price-input-area">
                                        <div class="half-input-wrapper">
                                            <div class="single">
                                                <label for="min_price">Min price</label>
                                                <input id="min_price" name="min_price" type="number" step="0.01" min="0" value="<?= htmlspecialchars($minPrice, ENT_QUOTES, 'UTF-8'); ?>" placeholder="0">
                                            </div>
                                            <div class="single">
                                                <label for="max_price">Max price</label>
                                                <input id="max_price" name="max_price" type="number" step="0.01" min="0" value="<?= htmlspecialchars($maxPrice, ENT_QUOTES, 'UTF-8'); ?>" placeholder="0">
                                            </div>
                                        </div>
                                        <div class="filter-value-min-max">
                                            <button type="submit" class="rts-btn btn-primary w-100">Apply Price</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="single-filter-box">
                                <h5 class="title">Product Categories</h5>
                                <div class="filterbox-body">
                                    <div class="category-wrapper">
                                        <div class="single-category">
                                            <input id="category-all" type="radio" name="category" value="" <?= $categorySlug === '' ? 'checked' : ''; ?>>
                                            <label for="category-all">All Categories</label>
                                        </div>
                                        <?php foreach ($categories as $index => $category): ?>
                                            <?php
                                            $slug = $category['slug'] ?? '';
                                            if ($slug === '') {
                                                continue;
                                            }
                                            $inputId = 'category-' . $index;
                                            ?>
                                            <div class="single-category">
                                                <input id="<?= htmlspecialchars($inputId, ENT_QUOTES, 'UTF-8'); ?>" type="radio" name="category" value="<?= htmlspecialchars($slug, ENT_QUOTES, 'UTF-8'); ?>" <?= $slug === $categorySlug ? 'checked' : ''; ?>>
                                                <label for="<?= htmlspecialchars($inputId, ENT_QUOTES, 'UTF-8'); ?>">
                                                    <?= htmlspecialchars($category['name'] ?? 'Category', ENT_QUOTES, 'UTF-8'); ?>
                                                </label>
                                            </div>
                                        <?php endforeach; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="d-flex flex-column gap-2 mt-3">
                                <button type="submit" class="rts-btn btn-primary">Filter Results</button>
                                <a href="shop-grid-sidebar.php" class="rts-btn btn-primary border-only">Reset Filters</a>
                            </div>
                        </form>
                    </div>
                </div>
                <div class="col-xl-9 col-lg-12">
                    <div class="shop-products-wrapper ps--60 ps_lg--0">
                        <div class="filter-select-area">
                            <div class="top-filter">
                                <span>
                                    <?php if ($totalProducts > 0): ?>
                                        Showing <?= $startIndex; ?>–<?= $endIndex; ?> of <?= $totalProducts; ?> results
                                    <?php else: ?>
                                        No products found
                                    <?php endif; ?>
                                </span>
                                <form class="d-flex flex-wrap gap-3 align-items-center" method="get" action="">
                                    <input type="hidden" name="category" value="<?= htmlspecialchars($categorySlug, ENT_QUOTES, 'UTF-8'); ?>">
                                    <input type="hidden" name="min_price" value="<?= htmlspecialchars($minPrice, ENT_QUOTES, 'UTF-8'); ?>">
                                    <input type="hidden" name="max_price" value="<?= htmlspecialchars($maxPrice, ENT_QUOTES, 'UTF-8'); ?>">
                                    <input type="hidden" name="page" value="1">
                                    <div class="search-box">
                                        <input type="search" name="q" value="<?= htmlspecialchars($searchQuery, ENT_QUOTES, 'UTF-8'); ?>" placeholder="Search products...">
                                    </div>
                                    <div class="select-box">
                                        <select name="sort">
                                            <?php foreach ($sortOptions as $value => $label): ?>
                                                <option value="<?= htmlspecialchars($value, ENT_QUOTES, 'UTF-8'); ?>" <?= $sortOption === $value ? 'selected' : ''; ?>>
                                                    <?= htmlspecialchars($label, ENT_QUOTES, 'UTF-8'); ?>
                                                </option>
                                            <?php endforeach; ?>
                                        </select>
                                    </div>
                                    <button type="submit" class="rts-btn btn-primary border-only">Apply</button>
                                </form>
                            </div>
                        </div>
                        <div class="shop-grid-products mt--30">
                            <div class="row g-4">
                                <?php if (!empty($productCards)): ?>
                                    <?php foreach ($productCards as $card): ?>
                                        <div class="col-xl-4 col-md-6 col-sm-6">
                                            <?php render_product_card($card); ?>
                                        </div>
                                    <?php endforeach; ?>
                                <?php else: ?>
                                    <div class="col-12">
                                        <div class="no-data-message text-center py-5">
                                            <p class="mb-0">No products match your filters right now. Try adjusting your search.</p>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>

                        <?php if (($pagination['total_pages'] ?? 1) > 1): ?>
                            <div class="pagination-area mt--40">
                                <ul class="pagination justify-content-center">
                                    <?php if (!empty($pagination['has_prev'])): ?>
                                        <li>
                                            <a href="shop-grid-sidebar.php<?= build_shop_query_parameters($baseQuery, ['page' => $pagination['current_page'] - 1]); ?>">
                                                <i class="fa-regular fa-chevron-left"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    <?php for ($p = 1; $p <= ($pagination['total_pages'] ?? 1); $p++): ?>
                                        <li class="<?= $p === ($pagination['current_page'] ?? 1) ? 'active' : ''; ?>">
                                            <a href="shop-grid-sidebar.php<?= build_shop_query_parameters($baseQuery, ['page' => $p]); ?>"><?= $p; ?></a>
                                        </li>
                                    <?php endfor; ?>
                                    <?php if (!empty($pagination['has_next'])): ?>
                                        <li>
                                            <a href="shop-grid-sidebar.php<?= build_shop_query_parameters($baseQuery, ['page' => $pagination['current_page'] + 1]); ?>">
                                                <i class="fa-regular fa-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

<?php include 'components/layout/page-end.php'; ?>

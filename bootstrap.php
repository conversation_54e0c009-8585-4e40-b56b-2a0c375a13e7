<?php

/**
 * Application Bootstrap
 * 
 * Initializes the application with MongoDB support
 */

// Load Composer autoloader
require_once __DIR__ . '/vendor/autoload.php';

// Load environment variables
require_once __DIR__ . '/src/Database/EnvLoader.php';
RCF\Database\EnvLoader::load();

// Load existing utilities
require_once __DIR__ . '/components/utils.php';

function load_sample_content(): array
{
    static $cache = null;
    if ($cache === null) {
        $path = __DIR__ . '/config/sample_content.php';
        if (is_file($path)) {
            $data = require $path;
            $cache = is_array($data) ? $data : ['categories' => [], 'products' => []];
        } else {
            $cache = ['categories' => [], 'products' => []];
        }
    }

    return $cache;
}

function load_sample_categories(): array
{
    $content = load_sample_content();
    $categories = $content['categories'] ?? [];
    return is_array($categories) ? $categories : [];
}

function load_sample_products(): array
{
    $content = load_sample_content();
    $products = $content['products'] ?? [];
    return is_array($products) ? $products : [];
}

// Initialize MongoDB connection (lazy loading)
function getMongoConnection() {
    static $connection = null;
    if ($connection === null) {
        $connection = \RCF\Database\MongoConnection::getInstance();
    }
    return $connection;
}

// Helper functions for repositories
function getCategoryRepository() {
    static $repo = null;
    if ($repo === null) {
        $repo = new \RCF\Repositories\CategoryRepository();
    }
    return $repo;
}

function getProductRepository() {
    static $repo = null;
    if ($repo === null) {
        $repo = new \RCF\Repositories\ProductRepository();
    }
    return $repo;
}

// Database helper functions with enhanced error handling
function db_get_categories($status = 'active') {
    try {
        $repo = getCategoryRepository();
        if ($status === 'all') {
            return $repo->find();
        }
        return $repo->getCategoriesByStatus($status);
    } catch (Exception $e) {
        $errorInfo = \RCF\Database\ErrorHandler::handle($e, 'get_categories', ['status' => $status]);
        \RCF\Database\Logger::error('Failed to get categories', [
            'status' => $status,
            'error_type' => $errorInfo['type'],
            'recoverable' => $errorInfo['recoverable']
        ]);
        return [];
    }
}

function db_get_category_by_slug($slug) {
    try {
        $repo = getCategoryRepository();
        return $repo->findBySlug($slug);
    } catch (Exception $e) {
        error_log('Failed to get category by slug: ' . $e->getMessage());
        return null;
    }
}

function db_get_products($filters = [], $page = 1, $limit = 12) {
    try {
        $repo = getProductRepository();
        return $repo->getProductsWithFilters($filters, $page, $limit);
    } catch (Exception $e) {
        error_log('Failed to get products: ' . $e->getMessage());
        return ['items' => [], 'pagination' => []];
    }
}

function db_get_product_by_slug($slug) {
    try {
        $repo = getProductRepository();
        return $repo->findBySlug($slug);
    } catch (Exception $e) {
        error_log('Failed to get product by slug: ' . $e->getMessage());
        return null;
    }
}

function db_get_featured_products($limit = 10) {
    try {
        $repo = getProductRepository();
        return $repo->getFeaturedProducts($limit);
    } catch (Exception $e) {
        error_log('Failed to get featured products: ' . $e->getMessage());
        return [];
    }
}

function db_get_top_selling_products($limit = 10) {
    try {
        $repo = getProductRepository();
        return $repo->getTopSellingProducts($limit);
    } catch (Exception $e) {
        error_log('Failed to get top selling products: ' . $e->getMessage());
        return [];
    }
}

function db_get_sale_products($limit = 10) {
    try {
        $repo = getProductRepository();
        return $repo->getSaleProducts($limit);
    } catch (Exception $e) {
        error_log('Failed to get sale products: ' . $e->getMessage());
        return [];
    }
}

function db_search_products($searchTerm, $filters = [], $limit = 20) {
    try {
        $repo = getProductRepository();
        return $repo->searchProducts($searchTerm, $filters, $limit);
    } catch (Exception $e) {
        error_log('Failed to search products: ' . $e->getMessage());
        return [];
    }
}

function db_increment_product_views($productId) {
    try {
        $repo = getProductRepository();
        return $repo->incrementViews($productId);
    } catch (Exception $e) {
        error_log('Failed to increment product views: ' . $e->getMessage());
        return false;
    }
}

// Check if MongoDB is available and working (real MongoDB, not mock)
function is_mongodb_available() {
    // Allow forcing fallback via environment
    $disableMongo = ($_ENV['DB_DISABLE_MONGO'] ?? getenv('DB_DISABLE_MONGO') ?? '0');
    if (is_string($disableMongo)) {
        $disableMongo = strtolower(trim($disableMongo));
        if (in_array($disableMongo, ['1', 'true', 'yes', 'on'], true)) {
            return false;
        }
    }

    try {
        $connection = getMongoConnection();
        // First check if using mock
        if ($connection->isUsingMock()) {
            return false;
        }
        // Then test real connection
        return $connection->testConnection();
    } catch (Exception $e) {
        return false;
    }
}

// Check if database (MongoDB or mock) is available
function is_database_available() {
    try {
        $connection = getMongoConnection();
        return $connection->testConnection();
    } catch (Exception $e) {
        return false;
    }
}

// Fallback JSON helpers for original data store
function fallback_read_json_file(string $path): array {
    try {
        if (!file_exists($path) || !is_readable($path)) {
            \RCF\Database\Logger::error('Fallback JSON missing or unreadable', ['path' => $path]);
            return [];
        }
        $contents = file_get_contents($path);
        if ($contents === false || $contents === '') {
            \RCF\Database\Logger::error('Fallback JSON empty or unreadable', ['path' => $path]);
            return [];
        }
        $data = json_decode($contents, true);
        if (!is_array($data)) {
            \RCF\Database\Logger::error('Fallback JSON decode failed', [
                'path' => $path,
                'json_error' => function_exists('json_last_error_msg') ? json_last_error_msg() : 'unknown'
            ]);
            return [];
        }
        return $data;
    } catch (\Throwable $e) {
        \RCF\Database\Logger::error('Fallback JSON exception', [
            'path' => $path,
            'message' => $e->getMessage(),
        ]);
        return [];
    }
}

function fallback_get_categories(): array {
    $adminJsonPath = __DIR__ . '/storage/admin.json';
    $data = fallback_read_json_file($adminJsonPath);
    $items = $data['items'] ?? [];
    if (!is_array($items)) {
        $items = [];
    }
    $slugIndex = [];
    foreach ($items as &$item) {
        if (!isset($item['status']) || $item['status'] === '') {
            $item['status'] = 'active';
        }
        if (isset($item['slug'])) {
            $slugIndex[$item['slug']] = true;
        }
    }
    unset($item);

    foreach (load_sample_categories() as $sampleCategory) {
        $slug = $sampleCategory['slug'] ?? null;
        if ($slug !== null && isset($slugIndex[$slug])) {
            continue;
        }

        $sampleCategory['status'] = $sampleCategory['status'] ?? 'active';

        if (!isset($sampleCategory['created_at'])) {
            $sampleCategory['created_at'] = date('Y-m-d H:i:s');
        }
        if (!isset($sampleCategory['updated_at'])) {
            $sampleCategory['updated_at'] = $sampleCategory['created_at'];
        }

        $items[] = $sampleCategory;
        if ($slug !== null) {
            $slugIndex[$slug] = true;
        }
    }

    return array_values($items);
}

function fallback_products_collection(): array
{
    static $products = null;
    if ($products === null) {
        $products = load_sample_products();
    }

    return is_array($products) ? $products : [];
}

function fallback_filter_products_collection(array $products, array $filters): array
{
    $status = $filters['status'] ?? 'active';
    $searchTerm = isset($filters['search']) ? trim((string) $filters['search']) : '';
    $minPrice = isset($filters['min_price']) && $filters['min_price'] !== '' ? (float) $filters['min_price'] : null;
    $maxPrice = isset($filters['max_price']) && $filters['max_price'] !== '' ? (float) $filters['max_price'] : null;
    $categoryId = $filters['category_id'] ?? null;
    $categorySlug = $filters['category_slug'] ?? null;
    $featured = $filters['featured'] ?? null;
    $onSale = $filters['on_sale'] ?? null;

    $filtered = array_filter($products, function (array $product) use ($status, $searchTerm, $minPrice, $maxPrice, $categoryId, $categorySlug, $featured, $onSale) {
        if ($status !== 'all' && ($product['status'] ?? 'active') !== $status) {
            return false;
        }

        if ($categoryId !== null && ($product['category_id'] ?? null) !== $categoryId) {
            return false;
        }

        if ($categorySlug !== null && ($product['category_slug'] ?? null) !== $categorySlug) {
            return false;
        }

        $basePrice = $product['price'] ?? null;
        $salePrice = $product['sale_price'] ?? null;
        $effectivePrice = ($salePrice !== null && $salePrice !== '' && is_numeric($salePrice)) ? (float) $salePrice : (is_numeric($basePrice) ? (float) $basePrice : null);

        if ($minPrice !== null && ($effectivePrice === null || $effectivePrice < $minPrice)) {
            return false;
        }

        if ($maxPrice !== null && ($effectivePrice === null || $effectivePrice > $maxPrice)) {
            return false;
        }

        if ($featured !== null) {
            $isFeatured = !empty($product['featured']);
            if ($featured && !$isFeatured) {
                return false;
            }
            if ($featured === false && $isFeatured) {
                return false;
            }
        }

        if ($onSale !== null) {
            $hasDiscount = is_numeric($basePrice) && is_numeric($salePrice) && (float) $salePrice < (float) $basePrice;
            if ($onSale && !$hasDiscount) {
                return false;
            }
            if ($onSale === false && $hasDiscount) {
                return false;
            }
        }

        if ($searchTerm !== '') {
            $haystacks = [
                $product['name'] ?? '',
                $product['description'] ?? '',
            ];

            if (isset($product['tags']) && is_array($product['tags'])) {
                $haystacks[] = implode(' ', $product['tags']);
            }

            $matched = false;
            foreach ($haystacks as $haystack) {
                if (is_string($haystack) && stripos($haystack, $searchTerm) !== false) {
                    $matched = true;
                    break;
                }
            }

            if (!$matched) {
                return false;
            }
        }

        return true;
    });

    return array_values($filtered);
}

function fallback_sort_products_collection(array $products, ?string $sortOption): array
{
    $sort = $sortOption ?? '';

    $compareByNumeric = function ($a, $b, string $field, bool $descending = false): int {
        $aValue = isset($a[$field]) && is_numeric($a[$field]) ? (float) $a[$field] : 0.0;
        $bValue = isset($b[$field]) && is_numeric($b[$field]) ? (float) $b[$field] : 0.0;
        if ($aValue === $bValue) {
            return 0;
        }
        if ($descending) {
            return ($aValue < $bValue) ? 1 : -1;
        }
        return ($aValue > $bValue) ? 1 : -1;
    };

    $compareByPrice = function ($a, $b, bool $descending = false) use ($compareByNumeric) {
        $priceFor = function (array $product): float {
            $salePrice = $product['sale_price'] ?? null;
            if (is_numeric($salePrice)) {
                return (float) $salePrice;
            }
            return isset($product['price']) && is_numeric($product['price']) ? (float) $product['price'] : 0.0;
        };

        $aValue = $priceFor($a);
        $bValue = $priceFor($b);
        if ($aValue === $bValue) {
            return 0;
        }
        if ($descending) {
            return ($aValue < $bValue) ? 1 : -1;
        }
        return ($aValue > $bValue) ? 1 : -1;
    };

    usort($products, function ($a, $b) use ($sort, $compareByNumeric, $compareByPrice) {
        return match ($sort) {
            'price_asc' => $compareByPrice($a, $b, false),
            'price_desc' => $compareByPrice($a, $b, true),
            'name_asc' => strcasecmp($a['name'] ?? '', $b['name'] ?? ''),
            'name_desc' => strcasecmp($b['name'] ?? '', $a['name'] ?? ''),
            'popularity' => $compareByNumeric($a, $b, 'views', true),
            'sales' => $compareByNumeric($a, $b, 'sales_count', true),
            default => $compareByNumeric($a, $b, 'sales_count', true),
        };
    });

    return $products;
}

function fallback_paginate_products(array $products, int $page, int $limit): array
{
    $total = count($products);
    $limit = max(1, $limit);
    $totalPages = max(1, (int) ceil($total / $limit));
    $page = max(1, min($page, $totalPages));
    $offset = ($page - 1) * $limit;

    $items = array_slice($products, $offset, $limit);

    return [
        'items' => array_values($items),
        'pagination' => [
            'current_page' => $page,
            'per_page' => $limit,
            'total' => $total,
            'total_pages' => $totalPages,
            'has_next' => $page < $totalPages,
            'has_prev' => $page > 1,
        ],
    ];
}

function fallback_get_products(array $filters = [], int $page = 1, int $limit = 12): array
{
    $products = fallback_products_collection();
    $filtered = fallback_filter_products_collection($products, $filters);

    if (!empty($filters['sort'])) {
        $filtered = fallback_sort_products_collection($filtered, $filters['sort']);
    }

    return fallback_paginate_products($filtered, $page, $limit);
}

function fallback_get_featured_products(int $limit = 10): array
{
    $filters = ['featured' => true, 'status' => 'active', 'sort' => 'sales'];
    $result = fallback_get_products($filters, 1, $limit);
    return $result['items'];
}

function fallback_get_top_selling_products(int $limit = 10): array
{
    $products = fallback_products_collection();
    $filtered = fallback_filter_products_collection($products, ['status' => 'active']);
    $sorted = fallback_sort_products_collection($filtered, 'sales');
    if ($limit > 0) {
        $sorted = array_slice($sorted, 0, $limit);
    }
    return $sorted;
}

function fallback_get_sale_products(int $limit = 10): array
{
    $filters = ['on_sale' => true, 'status' => 'active', 'sort' => 'sales'];
    $result = fallback_get_products($filters, 1, $limit);
    return $result['items'];
}

// Wrapper functions that fall back to JSON if real MongoDB is not available
function get_categories($status = 'active') {
    if (is_database_available()) {
        $categories = db_get_categories($status);
        if (!empty($categories)) {
            return $categories;
        }
    }

    $categories = fallback_get_categories();
    if ($status !== 'all') {
        $categories = array_values(array_filter($categories, function ($cat) use ($status) {
            return ($cat['status'] ?? 'active') === $status;
        }));
    }

    return $categories;
}

function get_category_by_slug($slug) {
    if (is_database_available()) {
        $category = db_get_category_by_slug($slug);
        if ($category !== null) {
            return $category;
        }
    }

    foreach (fallback_get_categories() as $category) {
        if (($category['slug'] ?? null) === $slug) {
            return $category;
        }
    }

    return null;
}

function get_products_with_filters($filters = [], $page = 1, $limit = 12): array
{
    if (is_database_available()) {
        return db_get_products($filters, $page, $limit);
    }

    return fallback_get_products($filters, $page, $limit);
}

function get_product_by_slug($slug)
{
    if (is_database_available()) {
        $product = db_get_product_by_slug($slug);
        if ($product !== null) {
            return $product;
        }
    }

    foreach (fallback_get_products(['status' => 'all'], 1, PHP_INT_MAX)['items'] as $product) {
        if (($product['slug'] ?? null) === $slug) {
            return $product;
        }
    }

    return null;
}

function get_featured_products($limit = 10): array
{
    $products = [];
    if (is_database_available()) {
        $products = db_get_featured_products($limit);
    }

    if (!empty($products)) {
        return $products;
    }

    return fallback_get_featured_products($limit);
}

function get_top_selling_products($limit = 10): array
{
    $products = [];
    if (is_database_available()) {
        $products = db_get_top_selling_products($limit);
    }

    if (!empty($products)) {
        return $products;
    }

    return fallback_get_top_selling_products($limit);
}

function get_sale_products($limit = 10): array
{
    $products = [];
    if (is_database_available()) {
        $products = db_get_sale_products($limit);
    }

    if (!empty($products)) {
        return $products;
    }

    return fallback_get_sale_products($limit);
}

function search_products($searchTerm, $filters = [], $limit = 20): array
{
    if (is_database_available()) {
        return db_search_products($searchTerm, $filters, $limit);
    }

    $filters['search'] = $searchTerm;
    return fallback_get_products($filters, 1, $limit)['items'];
}
